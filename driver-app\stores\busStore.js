import { create } from 'zustand';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { fetchBusDataFromAPI } from '../scripts/BusStopRestAPI';

// Create a Zustand store with persistence
const useBusStore = create((set, get) => ({
  // Bus data state
  busData: null,
  busStops: [],
  busHistory: [],
  isLoading: false,
  error: null,
  dataSource: null, // Add this to track where data came from

  // Initialize the store with data from AsyncStorage
  initializeStore: async (busNumber) => {
    set({ isLoading: true, error: null });

    try {
      // Removed cache checking logic. Always fetching fresh data.
      console.log(`[BusStore] Always fetching fresh data for bus ${busNumber} during initialization`);
      const apiData = await get().fetchBusData(busNumber);

      // fetchBusData already sets busData, busStops, busHistory, isLoading, and dataSource in the store.
      // It also caches the data in AsyncStorage.
      // It returns an object like: { success: true, ...apiData, source: 'database' }
      // or { success: false, error: message, source: 'database' }

      // So, we just need to return the result from fetchBusData.
      // The source will be 'database' as set by fetchBusData.
      return apiData; // apiData already contains success and source
    } catch (error) {
      // This catch block might be redundant if fetchBusData handles its own errors
      // and sets the store state accordingly, which it does.
      // However, keeping it for safety in case fetchBusData itself throws an unhandled error
      // or if there's a desire to log/handle initialization-specific errors differently.
      console.error('[BusStore] Error during forced fresh initialization of bus store:', error);
      // Ensure store state reflects the error if not already set by fetchBusData's catch.
      set({ error: error.message || 'Unknown error during initialization', isLoading: false, dataSource: 'database' });
      return {
        success: false,
        error: error.message || 'Unknown error during initialization',
        source: 'database'
      };
    }
  },

  // Fetch bus data from DynamoDB
  fetchBusData: async (busNumber) => {
    set({ isLoading: true, error: null });

    try {
      console.log(`[BusStore] Fetching FRESH data from REST API for bus ${busNumber}`);
      // Fetch bus data from REST API instead of DynamoDB
      const apiData = await fetchBusDataFromAPI(busNumber);
      // console.log(`[BusStore] Received data from REST API for bus ${busNumber}:`, JSON.stringify(apiData, null, 2));

      if (!apiData) {
        console.error(`[BusStore] No data found in API for bus ${busNumber}`);
        throw new Error(`No data found for bus ${busNumber}`);
      }
      // Check if apiData.stops exists and is an array
      const stops = apiData.stops && Array.isArray(apiData.stops) ? apiData.stops : [];
      const history = apiData.history && Array.isArray(apiData.history) ? apiData.history : [];
      console.log(`[BusStore] Successfully fetched data for bus ${busNumber} with ${stops.length} stops and ${history.length} history items`);

      // Store in Zustand state
      set({
        busData: apiData,
        busStops: stops,
        busHistory: history,
        isLoading: false,
        dataSource: 'database'
      });

      // Cache all data in AsyncStorage
      await AsyncStorage.setItem(
        `bus_data_${busNumber}`,
        JSON.stringify({
          busData: apiData,
          timestamp: Date.now()
        })
      );
      // Also cache separately for background task
      await AsyncStorage.setItem(`busStops_${busNumber}`, JSON.stringify(stops));
      await AsyncStorage.setItem(`busHistory_${busNumber}`, JSON.stringify(history));

      console.log(`[BusStore] Cached fresh data for bus ${busNumber} in AsyncStorage`);
      return {
        success: true,
        ...apiData, 
        source: 'database'
      };
    } catch (error) {
      console.error('Error fetching bus data:', error);
      set({ error: error.message, isLoading: false });
      return {
        success: false,
        error: error.message
      };
    }
  },

  clearStore: async (busNumber) => {
    set({ busData: null, busStops: [], busHistory: [], error: null });

    try {
      // Remove all cached data
      await AsyncStorage.removeItem(`bus_data_${busNumber}`);
      await AsyncStorage.removeItem(`busStops_${busNumber}`);
      await AsyncStorage.removeItem(`busHistory_${busNumber}`);
    } catch (error) {
      console.error('Error clearing bus store:', error);
    }
  },
  // Add this action to update bus history
  setBusHistory: async (busNumber, history) => {
    try {
      set({ busHistory: history });

      // Also update AsyncStorage
      await AsyncStorage.setItem(`busHistory_${busNumber}`, JSON.stringify(history));
      console.log(`[BusStore] Updated history for bus ${busNumber}`);

      return true;
    } catch (error) {
      console.error('[BusStore] Error updating history:', error);
      return false;
    }
  },

}));

export default useBusStore;