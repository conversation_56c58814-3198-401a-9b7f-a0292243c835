import * as Location from 'expo-location';
import { updateBusLocation, fetchBusStopDetails, updateBusHistory, updateBusStatus, clearBusCoordinates, getBusStops } from './DynamoData';
import { Vibration, AppState, Platform } from 'react-native';
import * as TaskManager from 'expo-task-manager';
import * as KeepAwake from 'expo-keep-awake';
import * as BackgroundFetch from 'expo-background-fetch';

const DEFAULT_INTERVAL = 5000;
const STATIONARY_INTERVAL = 2000;
const BACKGROUND_TASK_NAME = 'BACKGROUND_LOCATION_TRACKING';
const BACKGROUND_LOCATION_TASK = 'background-location-task';
let timerId = null;
let intervalId = null; // For setInterval
let currentBusNumber = null;
let isTracking = false;  // Tracking flag
const REACH_THRESHOLD = 100; // 100 meters threshold
let appState = 'active';

// New variables for sequential stop tracking
let busStopsSequence = [];
let currentStopIndex = 0;
let lastReachedStop = null;
let isReturnJourney = false; // Flag to track if we're on the return journey

// Function to calculate distance between two points using Haversine formula
function calculateDistance(lat1, lon1, lat2, lon2) {
  const R = 6371; // Earth's radius in kilometers
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLon = (lon2 - lon1) * Math.PI / 180;
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
    Math.sin(dLon / 2) * Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c * 1000; // Convert to meters
}

/**
 * Find the nearest bus stop to the current location
 * @param {object} currentCoords - Current coordinates {latitude, longitude}
 * @param {Array} stopsArray - Array of bus stops
 * @returns {object} - Object containing nearest stop index and distance
 */
function findNearestStopIndex(currentCoords, stopsArray) {
  if (!stopsArray || stopsArray.length === 0) {
    console.log("No stops to find nearest from");
    return { nearestIndex: -1, minDistance: Infinity };
  }

  let minDistance = Infinity;
  let nearestIndex = -1;

  console.log("Finding nearest stop from current position:", currentCoords);

  // Calculate distance to each stop and find the minimum
  stopsArray.forEach((stop, index) => {
    if (!stop || !stop.coordinates) {
      console.log(`Stop at index ${index} has invalid coordinates`);
      return;
    }

    const [stopLat, stopLon] = stop.coordinates.split(',').map(Number);
    const distance = calculateDistance(
      currentCoords.latitude,
      currentCoords.longitude,
      stopLat,
      stopLon
    );

    console.log(`Distance to stop "${stop.stopName}" (index ${index}): ${distance.toFixed(2)} meters`);

    if (distance < minDistance) {
      minDistance = distance;
      nearestIndex = index;
    }
  });

  console.log(`Nearest stop is at index ${nearestIndex} with distance ${minDistance.toFixed(2)} meters`);
  return { nearestIndex, minDistance };
}

// Modified function to check if we've reached the current stop in sequence
async function checkCurrentBusStop(currentCoords) {
  // If we don't have any stops
  if (busStopsSequence.length === 0) {
    console.log("No bus stops to track");
    return null;
  }

  // If we've reached the end of the sequence, find the nearest stop
  if (currentStopIndex >= busStopsSequence.length) {
    console.log("Reached end of bus stops sequence. Finding nearest stop...");

    const { nearestIndex, minDistance } = findNearestStopIndex(currentCoords, busStopsSequence);

    if (nearestIndex === -1) {
      console.log("Could not find a valid nearest stop");
      return null;
    }

    // If nearest stop is at the beginning, assume we're looping back to start
    if (nearestIndex === 0) {
      console.log("Nearest stop is the first stop. Looping back to start of route.");
      currentStopIndex = 0;
      lastReachedStop = null;
      isReturnJourney = false;
    }
    // If nearest stop is near the end, assume we're returning
    else if (nearestIndex >= busStopsSequence.length - 2) {
      console.log("Nearest stop is near the end. Starting return journey.");
      // Create a reversed copy of the bus stops sequence
      busStopsSequence = [...busStopsSequence].reverse();
      currentStopIndex = 0;
      lastReachedStop = null;
      isReturnJourney = true;
      console.log("Route reversed for return journey. New first stop:", busStopsSequence[0].stopName);
    }
    // Otherwise, continue from the nearest stop
    else {
      console.log(`Continuing from nearest stop at index ${nearestIndex}`);
      currentStopIndex = nearestIndex;
      // Don't reset lastReachedStop to avoid duplicate notifications
    }
  }

  // Get the current stop we're tracking
  const currentStop = busStopsSequence[currentStopIndex];

  if (!currentStop || !currentStop.coordinates) {
    console.log("Invalid stop data for current stop");
    currentStopIndex++; // Skip this stop and move to the next one
    return null;
  }

  const [stopLat, stopLon] = currentStop.coordinates.split(',').map(Number);

  // Calculate distance to current stop
  const distance = calculateDistance(
    currentCoords.latitude,
    currentCoords.longitude,
    stopLat,
    stopLon
  );

  const journeyType = isReturnJourney ? "RETURN" : "FORWARD";
  console.log(`[${journeyType}] Distance to next stop "${currentStop.stopName}": ${distance.toFixed(2)} meters`);

  // Check if we've reached the current stop
  if (distance <= REACH_THRESHOLD &&
    currentBusNumber &&
    lastReachedStop !== currentStop.stopName) {

    const stopPrefix = isReturnJourney ? "RETURN: " : "";
    console.log(`${stopPrefix}Reached bus stop: ${currentStop.stopName}`);

    // Update history with journey direction indicator
    await updateBusHistory(currentBusNumber, `${currentStop.stopName}`);
    lastReachedStop = currentStop.stopName;

    // Move to the next stop in sequence
    currentStopIndex++;

    // Vibrate to notify driver
    Vibration.vibrate(1000);

    const remainingStops = busStopsSequence.length - currentStopIndex;
    console.log(`Moving to next stop. ${remainingStops} stops remaining in ${journeyType} journey.`);
  }

  return {
    stopName: currentStop.stopName,
    latitude: stopLat,
    longitude: stopLon,
    distance: distance,
    isNextStop: true,
    isReturnJourney: isReturnJourney
  };
}

// Define the background task
TaskManager.defineTask(BACKGROUND_LOCATION_TASK, async ({ data, error }) => {
  if (error) {
    console.error('Background location task error:', error);
    return BackgroundFetch.BackgroundFetchResult.Failed;
  }

  if (!data) {
    console.log('No data received in background task');
    return BackgroundFetch.BackgroundFetchResult.NoData;
  }

  if (!isTracking || !currentBusNumber) {
    console.log('Not tracking or no bus number, exiting background task');
    return BackgroundFetch.BackgroundFetchResult.NoData;
  }

  try {
    const { locations } = data;
    const location = locations[0];

    if (!location) {
      console.log('No location data in background task');
      return BackgroundFetch.BackgroundFetchResult.NoData;
    }

    const coordinates = {
      latitude: location.coords.latitude,
      longitude: location.coords.longitude
    };

    console.log('Background location update:', coordinates);

    // Check if we're at a bus stop
    await checkCurrentBusStop(coordinates);

    // Update the bus location in the database
    await updateBusLocation(currentBusNumber, coordinates);

    console.log('Background location update successful');
    return BackgroundFetch.BackgroundFetchResult.NewData;
  } catch (error) {
    console.error('Error in background task:', error);
    return BackgroundFetch.BackgroundFetchResult.Failed;
  }
});

// SIMPLIFIED AppState handler for better Expo Go compatibility
const handleAppStateChange = (nextAppState) => {
  console.log('App state changed:', appState, '->', nextAppState);

  // Only care about active -> background and background -> active transitions
  const wasActive = appState === 'active';
  const isNowActive = nextAppState === 'active';
  const isNowBackground = nextAppState.match(/inactive|background/);

  appState = nextAppState;

  if (!isTracking) return;

  // App went to background
  if (wasActive && isNowBackground) {
    console.log('App went to background');

    // Clear any existing timers
    if (timerId) {
      clearTimeout(timerId);
      timerId = null;
    }

    // We don't need to set up a new interval here as the background location task will handle updates
  }
  // App came to foreground
  else if (!wasActive && isNowActive) {
    console.log('App came to foreground');

    // Clear background interval
    if (intervalId) {
      clearInterval(intervalId);
      intervalId = null;
    }

    // Resume foreground tracking
    if (!timerId) {
      console.log('Resuming foreground tracking');
      getCurrentLocation();
    }
  }
};

// SIMPLIFIED location tracking function for better Expo Go compatibility
async function getCurrentLocation() {
  // Return early if tracking is stopped
  if (!isTracking) {
    console.log('Tracking stopped, exiting getCurrentLocation');
    return null;
  }

  try {
    console.log('Getting current location...');
    const location = await Location.getCurrentPositionAsync({
      accuracy: Location.Accuracy.High,
    });

    // Check tracking state again after getting location
    if (!isTracking) {
      console.log('Tracking stopped while getting location');
      return null;
    }

    const coordinates = {
      latitude: location.coords.latitude,
      longitude: location.coords.longitude
    };

    console.log('Current coordinates:', coordinates);

    const isMoving = location.coords.speed && location.coords.speed > 0.1;

    if (!isMoving) {
      console.log('Device not moving, using shorter interval');
      if (isTracking && appState === 'active') {
        timerId = setTimeout(getCurrentLocation, STATIONARY_INTERVAL);
      }
      return location.coords;
    }

    // Only update if still tracking and in foreground
    if (isTracking && appState === 'active') {
      console.log('Checking for bus stops...');
      await checkCurrentBusStop(coordinates);

      if (currentBusNumber) {
        console.log('Updating bus location in database...');
        await updateBusLocation(currentBusNumber, coordinates);
        Vibration.vibrate(200); // location updated
      }

      timerId = setTimeout(getCurrentLocation, DEFAULT_INTERVAL);
      console.log(`Next location update in ${DEFAULT_INTERVAL}ms`);
    }
    return location.coords;
  } catch (error) {
    console.error('Error getting location:', error);
    if (isTracking && appState === 'active') {
      timerId = setTimeout(getCurrentLocation, DEFAULT_INTERVAL);
    }
    return null;
  }
}

// SIMPLIFIED start tracking function for Expo Go compatibility
async function startTracking(busNumber) {
  console.log('Starting tracking for bus:', busNumber);

  // Clear any existing timers
  if (timerId) {
    clearTimeout(timerId);
    timerId = null;
  }

  if (intervalId) {
    clearInterval(intervalId);
    intervalId = null;
  }

  isTracking = true;
  currentBusNumber = busNumber;

  // Reset stop tracking variables
  currentStopIndex = 0;
  lastReachedStop = null;
  isReturnJourney = false;

  busStopsSequence = await getBusStops(busNumber);

  console.log('Bus stops sequence for bus', busNumber, ':', busStopsSequence);

  if (busStopsSequence && busStopsSequence.length > 0) {
    console.log(`Starting route with ${busStopsSequence.length} stops. First stop: ${busStopsSequence[0].stopName}`);
  } else {
    console.log('No stops found for this bus route');
  }

  // Request location permissions
  console.log('Requesting location permissions...');
  const { status: foregroundStatus } = await Location.requestForegroundPermissionsAsync();
  if (foregroundStatus !== 'granted') {
    console.error('Foreground location permission denied');
    return;
  }

  // Request background permissions
  const { status: backgroundStatus } = await Location.requestBackgroundPermissionsAsync();
  if (backgroundStatus !== 'granted') {
    console.warn('Background location permission denied - background tracking will not work');
  } else {
    console.log('Background location permission granted');

    try {
      // Register the background location task if it's not already registered
      const hasStarted = await Location.hasStartedLocationUpdatesAsync(BACKGROUND_LOCATION_TASK)
        .catch(() => false);

      if (!hasStarted) {
        console.log('Starting background location updates...');

        // Configure options based on platform
        const locationOptions = {
          accuracy: Location.Accuracy.Balanced,
          timeInterval: DEFAULT_INTERVAL,
          distanceInterval: 10, // minimum change (in meters) before receiving updates
          // Android specific
          foregroundService: {
            notificationTitle: "Bus Tracking Active",
            notificationBody: "Your location is being tracked for bus route updates",
            notificationColor: "#FF0000",
          },
          // iOS specific
          activityType: Location.ActivityType.AutomotiveNavigation,
          pausesUpdatesAutomatically: false,
        };

        // Add platform-specific options
        if (Platform.OS === 'android') {
          // Ensure the foreground service is properly configured for Android
          console.log('Configuring Android foreground service');
        } else if (Platform.OS === 'ios') {
          // iOS specific configurations
          console.log('Configuring iOS background tracking');
        }

        await Location.startLocationUpdatesAsync(BACKGROUND_LOCATION_TASK, locationOptions);
        console.log('Background location updates started');
      } else {
        console.log('Background location task already running');
      }
    } catch (error) {
      console.error('Error starting background location updates:', error);
      console.log('Falling back to foreground-only tracking');
    }
  }

  // Try to keep the device awake
  try {
    await KeepAwake.activateKeepAwakeAsync();
    console.log('Keep awake activated');
  } catch (error) {
    console.warn('Could not activate keep awake:', error);
  }

  // Set up app state change monitoring
  try {
    // Set up initial app state
    appState = AppState.currentState;
    console.log('Initial app state:', appState);

    // For React Native 0.65+
    AppState.addEventListener('change', handleAppStateChange);
    console.log('AppState listener added');
  } catch (error) {
    console.error('Error setting up AppState listener:', error);
  }

  updateBusStatus(busNumber, 'Active');
  console.log('Location tracking started for bus:', busNumber);

  // Start location tracking
  getCurrentLocation();
}

// SIMPLIFIED stop tracking function for Expo Go compatibility
async function stopTracking() {
  console.log('Stopping location tracking...');
  isTracking = false;

  // Try to deactivate keep awake
  try {
    await KeepAwake.deactivateKeepAwake();
    console.log('Keep awake deactivated');
  } catch (error) {
    console.warn('Error deactivating keep awake:', error);
  }

  // Clear any existing timers
  if (timerId) {
    clearTimeout(timerId);
    timerId = null;
  }

  if (intervalId) {
    clearInterval(intervalId);
    intervalId = null;
  }

  // Stop background location updates
  let locationStopped = false;
  try {
    if (TaskManager.isTaskDefined(BACKGROUND_LOCATION_TASK)) {
      const hasStarted = await Location.hasStartedLocationUpdatesAsync(BACKGROUND_LOCATION_TASK);
      if (hasStarted) {
        console.log('Stopping background location updates...');
        await Location.stopLocationUpdatesAsync(BACKGROUND_LOCATION_TASK);
        console.log('Background location updates stopped');
      }
      locationStopped = true;
    }
  } catch (error) {
    // Check if the error is specifically about the task not being found
    if (error.message && error.message.includes("not found")) {
      console.log('Task already unregistered, considering tracking stopped');
      locationStopped = true;
    } else {
      console.error('Error stopping background location task:', error);
    }
  }

  // Update bus status if we have a bus number
  if (currentBusNumber) {
    try {
      // Clear bus coordinates in the database
      await clearBusCoordinates(currentBusNumber);
      console.log('Bus coordinates cleared');
      
      // Update bus status to inactive
      await updateBusStatus(currentBusNumber, 'Inactive');
      console.log('Bus status updated to inactive');
    } catch (error) {
      console.error('Error updating bus status:', error);
    }
    
    // Clear the current bus number
    currentBusNumber = null;
  }

  // Reset tracking variables
  lastReachedStop = null;
  busStopsSequence = [];
  currentStopIndex = 0;
  isReturnJourney = false;

  console.log('Location tracking stopped completely');
  return locationStopped;
}

// Initialize function to set up location tracking
const initialize = () => {
  console.log('Initializing location tracker...');

  // Clean up any existing tracking state
  if (isTracking) {
    stopTracking();
  }

  // Register the background task if not already registered
  if (!TaskManager.isTaskDefined(BACKGROUND_LOCATION_TASK)) {
    console.log('Defining background location task');
    // Task is defined at the top of the file
  }

  console.log('Location tracker initialized and ready');
};

// Call initialize
initialize();

export { getCurrentLocation, startTracking, stopTracking };

