{"name": "driver-app", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@aws-sdk/client-dynamodb": "^3.758.0", "@aws-sdk/lib-dynamodb": "^3.758.0", "@expo/vector-icons": "^14.0.2", "@react-native-async-storage/async-storage": "^1.23.1", "@react-native-community/netinfo": "11.3.1", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "aws-sdk": "^2.1692.0", "axios": "^1.8.3", "depcheck": "^1.4.7", "expo": "~51.0.0", "expo-background-fetch": "~12.0.1", "expo-battery": "~8.0.1", "expo-blur": "~13.0.3", "expo-build-properties": "~0.12.5", "expo-constants": "~16.0.2", "expo-dev-client": "~4.0.29", "expo-device": "~6.0.2", "expo-file-system": "~17.0.1", "expo-font": "~12.0.10", "expo-haptics": "~13.0.1", "expo-keep-awake": "~13.0.2", "expo-linear-gradient": "~13.0.2", "expo-linking": "~6.3.1", "expo-location": "~17.0.1", "expo-media-library": "~16.0.5", "expo-notifications": "~0.28.19", "expo-print": "~13.0.1", "expo-router": "~3.5.24", "expo-sharing": "~12.0.1", "expo-splash-screen": "~0.27.7", "expo-status-bar": "~1.12.1", "expo-symbols": "~0.1.5", "expo-system-ui": "~3.0.7", "expo-task-manager": "~11.8.2", "expo-updates": "~0.25.28", "expo-web-browser": "~13.0.3", "i18next": "^24.2.3", "react": "18.2.0", "react-dom": "18.2.0", "react-i18next": "^15.4.1", "react-native": "0.74.5", "react-native-ble-plx": "^3.5.0", "react-native-dotenv": "^3.4.11", "react-native-event-listeners": "^1.0.7", "react-native-gesture-handler": "~2.16.1", "react-native-reanimated": "~3.10.1", "react-native-safe-area-context": "4.10.5", "react-native-screens": "3.31.1", "react-native-web": "~0.19.13", "react-native-webview": "13.8.6", "zustand": "^5.0.3"}, "devDependencies": {"@babel/core": "^7.25.2", "@react-native-community/cli": "^18.0.0", "@types/jest": "^29.5.12", "@types/react": "~18.3.12", "@types/react-test-renderer": "^18.3.0", "jest": "^29.2.1", "jest-expo": "~52.0.6", "react-test-renderer": "18.3.1", "typescript": "^5.3.3"}, "private": true}