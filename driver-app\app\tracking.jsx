import React, { useState, useEffect, useRef, useContext } from 'react';
import { Animated, View, Text, TouchableOpacity, ImageBackground, Alert, AppState, Platform, Linking } from 'react-native';
import FontAwesome6 from '@expo/vector-icons/FontAwesome6';
// import { MaterialIcons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { Easing } from 'react-native';
import createStyles from './Style_Schema/tracking_schema';
import MapsImage from '../assets/graphics/maps.png';
import MapsImageDark from '../assets/graphics/maps-dark.png';
import ColorModeToggle from '../components/ColorModeToggle';
import LogoutButton from '../components/LogoutButton';
import { ThemeContext } from '../contexts/ThemeContext';
import { useRoute } from '@react-navigation/native';
import * as TaskManager from 'expo-task-manager';
import * as Location from 'expo-location';
// import * as Updates from 'expo-updates';
import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import AsyncStorage from '@react-native-async-storage/async-storage';
// import axios from 'axios';
import {
  updateBusLocation,
  updateBusStatus,
  updateDriverStatus,
  clearBusHistory,
  // getBusHistory,
  // startNewJourney,
  // endJourney,
  // getBusCurrentCoordinates,  // Add this import
  clearBusCoordinates,        // Add this import
  updateDriverAssignedBus,
  updateBusDelayStatus
} from '../scripts/DynamoData';
import { EventRegister } from 'react-native-event-listeners';
import StopRideModal from '../components/StopRideModal';
// Add import for the ResumeRideModal component
import ResumeRideModal from '../components/ResumeRideModal';
import LastStopModal from '../components/LastStopModal';
import DropdownMenu from '../components/DropdownMenu';
// import { checkForUpdates, getCurrentVersion } from '../utils/updateUtils';
import ReleaseNotesModal from '../components/ReleaseNotesModal';
// import { CURRENT_RELEASE_NOTES } from '../constants/ReleaseNotes';
import { useTranslation } from 'react-i18next';
// import { fetchBusDataFromAPI } from '../scripts/BusStopRestAPI';
import useBusStore from '../stores/busStore';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import GPSStatusIndicator from '../components/GPSStatusIndicator';
import LoadingIndicator from '../components/LoadingIndicator';

// Add these variables outside the component for tracking delay
let lastCoordinates = null;
let stationaryStartTime = null;
// let delayCheckInterval = null;
const DELAY_THRESHOLD_METERS = 250;
const DELAY_THRESHOLD_MINUTES = 4;

// Add these constants at the top
const RETRY_DELAY = 2000; // 2 seconds
const MAX_RETRIES = 3;

// Add this helper function
const getLastKnownLocation = async () => {
  try {
    const lastLocation = await AsyncStorage.getItem('lastKnownLocation');
    return lastLocation ? JSON.parse(lastLocation) : null;
  } catch (error) {
    console.error('Error getting last known location:', error);
    return null;
  }
};

// Add these helper functions at the top of the file
const checkLocationAccuracy = async () => {
  try {
    // Force GPS to warm up
    const initialPosition = await Location.getCurrentPositionAsync({
      accuracy: Location.Accuracy.BestForNavigation,
      maximumAge: 0,
      timeout: 20000
    });

    return initialPosition.coords.accuracy <= 50; // Return true if accuracy is good
  } catch (error) {
    console.error('Error checking location accuracy:', error);
    return false;
  }
};

const requestLocationServicesEnabled = async () => {
  if (Platform.OS === 'android') {
    try {
      const enabled = await Location.hasServicesEnabledAsync();
      if (!enabled) {
        Alert.alert(
          'Enable GPS',
          'Please enable GPS for accurate tracking. Open Settings?',
          [
            {
              text: 'Open Settings',
              onPress: async () => {
                await Location.enableNetworkProviderAsync();
              },
            },
            { text: 'Cancel', style: 'cancel' }
          ]
        );
        return false;
      }
    } catch (error) {
      console.error('Error checking location services:', error);
      return false;
    }
  }
  return true;
};

// Background location task
TaskManager.defineTask('BACKGROUND_LOCATION_TASK', async ({ data, error }) => {
  if (error) {
    console.error('Location task error:', error);
    // If there's an error with the location task, set bus and driver to inactive
    try {
      const busNumber = await AsyncStorage.getItem('busNumber');
      const phoneNumber = await AsyncStorage.getItem('phoneNumber');

      if (busNumber && phoneNumber) {
        console.log(`Setting bus ${busNumber} and driver ${phoneNumber} to inactive due to location task error`);
        await updateDriverStatus(phoneNumber, 'Inactive');
        await updateBusStatus(busNumber, 'Inactive');
      }
    } catch (setInactiveError) {
      console.error('Error setting inactive status on location task error:', setInactiveError);
    }
    return;
  }

  if (data) {
    const { locations } = data;
    let location = locations[0];
    let retryCount = 0;

    // Add retry logic for null coordinates
    while (!location && retryCount < MAX_RETRIES) {
      console.log(`Coordinates null, retry attempt ${retryCount + 1}`);
      await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));
      try {
        const newLocation = await Location.getCurrentPositionAsync({
          accuracy: Location.Accuracy.BestForNavigation,
        });
        location = newLocation;
      } catch (error) {
        console.error('Error getting location in retry:', error);
      }
      retryCount++;
    }

    // If still null after retries, try to use last known location
    if (!location) {
      console.log('Using last known location as fallback');
      location = await getLastKnownLocation();
      if (!location) {
        console.error('No fallback location available');
        return;
      }
    }

    // Store the successful location as last known
    if (location.coords) {
      await AsyncStorage.setItem('lastKnownLocation', JSON.stringify(location));
    }

    const coordinates = {
      latitude: location.coords.latitude,
      longitude: location.coords.longitude
    };

    // Get busNumber from AsyncStorage instead of expecting it in the data object
    try {
      const busNumber = await AsyncStorage.getItem('busNumber');
      if (busNumber) { // Update the bus location in DynamoDB and get the result
        let busStopsData = null;
        let busHistoryData = null;

        try {
          // Get bus stops data
          const cachedStops = await AsyncStorage.getItem(`busStops_${busNumber}`);
          if (cachedStops) {
            busStopsData = JSON.parse(cachedStops);
            console.log(`[Background Task] Using cached bus stops (${busStopsData.length})`);
          }

          // Get bus history data
          const cachedHistory = await AsyncStorage.getItem(`busHistory_${busNumber}`);
          if (cachedHistory) {
            busHistoryData = JSON.parse(cachedHistory);
            console.log(`[Background Task] Using cached history (${busHistoryData.length})`);
          }
        } catch (cacheError) {
          console.error('[Background Task] Error retrieving cached data:', cacheError);
        }

        // Update the bus location in DynamoDB with both stops and history data
        const result = await updateBusLocation(
          busNumber,
          coordinates,
          busStopsData,
          busHistoryData
        );

        // Add more logging for debugging
        console.log(`Update result for bus ${busNumber}:`, result);
        // Check if bus is stationary (delayed)
        checkBusDelay(busNumber, coordinates);


        // If this is the last stop, trigger an event that the app can listen for
        if (result && result.isLastStop) {
          console.log(`Emitting lastStopReached event for bus ${busNumber}`);
          EventRegister.emit('lastStopReached', busNumber);
        }
      } else {
        console.error('Bus number not found in AsyncStorage');
      }
    } catch (error) {
      console.error('Error retrieving bus number:', error);
    }
  }
});
// Add this function to check if the bus is delayed
const checkBusDelay = async (busNumber, currentCoordinates) => {
  // Calculate distance if we have previous coordinates
  if (lastCoordinates) {
    const distance = calculateDistance(
      lastCoordinates.latitude,
      lastCoordinates.longitude,
      currentCoordinates.latitude,
      currentCoordinates.longitude
    );

    console.log(`Bus ${busNumber} moved ${distance.toFixed(2)} meters since last check`);

    // If bus moved more than threshold, reset the stationary timer
    if (distance > DELAY_THRESHOLD_METERS) {
      console.log(`Bus ${busNumber} moved more than ${DELAY_THRESHOLD_METERS} meters, resetting delay timer`);
      stationaryStartTime = null;

      // If the bus was previously marked as delayed, update to not delayed
      try {
        await updateBusDelayStatus(busNumber, false);
      } catch (error) {
        console.error('Error updating bus delay status to false:', error);
      }
    }
    // If bus is within threshold distance and timer not started, start the timer
    else if (!stationaryStartTime) {
      console.log(`Bus ${busNumber} is stationary, starting delay timer`);
      stationaryStartTime = new Date();
    }
    // If bus is within threshold and timer is running, check if delay threshold is reached
    else {
      const currentTime = new Date();
      const stationaryDuration = (currentTime - stationaryStartTime) / (1000 * 60); // in minutes

      console.log(`Bus ${busNumber} has been stationary for ${stationaryDuration.toFixed(2)} minutes`);

      // If stationary for more than threshold minutes, mark as delayed
      if (stationaryDuration >= DELAY_THRESHOLD_MINUTES) {
        console.log(`Bus ${busNumber} is delayed (stationary for ${stationaryDuration.toFixed(2)} minutes)`);
        try {
          await updateBusDelayStatus(busNumber, true);
        } catch (error) {
          console.error('Error updating bus delay status to true:', error);
        }
      }
    }
  }

  // Update last coordinates
  lastCoordinates = { ...currentCoordinates };
};
// Add this function to calculate distance between two points using Haversine formula
const calculateDistance = (lat1, lon1, lat2, lon2) => {
  const R = 6371; // Earth's radius in kilometers
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLon = (lon2 - lon1) * Math.PI / 180;
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
    Math.sin(dLon / 2) * Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c * 1000; // Convert to meters
};
// Add a task unregistration handler
TaskManager.defineTask('TASK_UNREGISTRATION_HANDLER', async () => {
  try {
    const busNumber = await AsyncStorage.getItem('busNumber');
    const phoneNumber = await AsyncStorage.getItem('phoneNumber');

    if (busNumber && phoneNumber) {
      console.log(`Setting bus ${busNumber} and driver ${phoneNumber} to inactive on task unregistration`);
      await updateDriverStatus(phoneNumber, 'Inactive');
      await updateBusStatus(busNumber, 'Inactive');
    }
  } catch (error) {
    console.error('Error setting inactive status on task unregistration:', error);
  }
});
// Configure notifications
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: false,
    shouldSetBadge: false,
  }),
});

export default function TrackingScreen() {
  const { theme, mode } = useContext(ThemeContext);
  const styles = createStyles(theme);
  const { t, i18n } = useTranslation();
  const route = useRoute();
  const navigation = useNavigation();
  const busNumber = route.params?.busNumber;

  const [isStarted, setIsStarted] = useState(false);
  const [waves, setWaves] = useState([]);

  const pressAnim = useRef(new Animated.Value(1)).current;
  const [isPressing, setIsPressing] = useState(false);
  // Add state for modal visibility
  const [showStopModal, setShowStopModal] = useState(false);
  // Add state for resume modal visibility
  const [showResumeModal, setShowResumeModal] = useState(false);
  // Add state for tracking current coordinates
  const [hasCurrentCoordinates, setHasCurrentCoordinates] = useState(false);
  // Add state for tracking if bus has history
  const [hasHistory, setHasHistory] = useState(false);
  // Add notification ID state to track active notification
  const [notificationId, setNotificationId] = useState(null);
  // Add state for last stop modal visibility
  const [showLastStopModal, setShowLastStopModal] = useState(false);
  // Add this state to track dropdown open state
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  // Add AppState reference to track app state changes
  const appState = useRef(AppState.currentState);
  const [showReleaseNotesModal, setShowReleaseNotesModal] = useState(false);
  const [releaseNotesVersion, setReleaseNotesVersion] = useState('');
  const [releaseNotesDate, setReleaseNotesDate] = useState('');
  const [releaseNotesContent, setReleaseNotesContent] = useState('');
  const {
    busData,
    busStops,
    isLoading,
    error,
    dataSource,
    initializeStore,
    clearStore
  } = useBusStore();

  // State to track if bus data is loading
  const [isBusDataLoading, setIsBusDataLoading] = useState(false);
  const loadingAnimation = useRef(new Animated.Value(0)).current;
  const [isStopping, setIsStopping] = useState(false);
  // Add state for tracking if ride is starting
  const [isStarting, setIsStarting] = useState(false);
  // Add rotation animation for the stopping overlay
  const rotateAnimation = useRef(new Animated.Value(0)).current;

  // initial load bus data
  useEffect(() => {
    const loadBusData = async () => {
      if (busNumber) {
        setIsBusDataLoading(true);
        console.log('[Tracking] Bus data loading started, showing loading animation');
        try {
          console.log(`[Tracking] Initializing bus store for bus ${busNumber}`);
          const result = await initializeStore(busNumber);
          // console.log(`[Tracking] Bus store initialized with result:`, result);

          if (result.success && result.stops) {
            console.log(`[Tracking] Bus data loaded successfully from ${result.source}`);
            // console.log(`[Tracking] Loaded busStops:`, result.stops);

            // Set hasCurrentCoordinates based on actual coordinates data
            const hasCoords = !!(result.currentCoordinates &&
              typeof result.currentCoordinates === 'object' &&
              result.currentCoordinates.latitude &&
              result.currentCoordinates.longitude &&
              !isNaN(result.currentCoordinates.latitude) &&
              !isNaN(result.currentCoordinates.longitude));
            setHasCurrentCoordinates(hasCoords);
            console.log(`hasCurrentCoordinates set to: ${hasCoords}`);

            // Set hasHistory based on actual history data
            const hasHistoryData = result.history && result.history.length > 0;
            setHasHistory(hasHistoryData);
            console.log(`hasHistory set to: ${hasHistoryData}`);

            // Show resume modal if needed
            if (hasCoords || hasHistoryData) {
              setShowResumeModal(true);
            }
          } else {
            console.error(`[Tracking] Failed to load valid data for bus ${busNumber}`);
            if (result) {
              console.error('Returned data:', result);
            }
          }
        } catch (error) {
          console.error('[Tracking] Error loading bus data:', error);
        } finally {
          setIsBusDataLoading(false);
          // setTimeout(() => {
          //   setIsBusDataLoading(false);
          //   console.log('[Tracking] Bus data loading complete, hiding loading animation');
          // }, 5000); // 500ms delay to ensure animation is visible
        }
      }
    };

    loadBusData();
    // Keep the lastStopReached event listener
    const lastStopListener = EventRegister.addEventListener('lastStopReached', (receivedBusNumber) => {
      console.log(`Received lastStopReached event for bus ${receivedBusNumber}, current bus: ${busNumber}`);
      if (receivedBusNumber === busNumber) {
        console.log('Showing LastStopModal');
        setShowLastStopModal(true);
      }
    });

    return () => {
      EventRegister.removeEventListener(lastStopListener);
    };
  }, [busNumber]);

  // Create loading animation
  useEffect(() => {
    console.log(`[Loading] Animation state changed to: ${isBusDataLoading}`); //
    if (isBusDataLoading) {
      Animated.loop(
        Animated.sequence([
          Animated.timing(loadingAnimation, {
            toValue: 1,
            duration: 800,
            easing: Easing.linear,
            useNativeDriver: true
          }),
          Animated.timing(loadingAnimation, {
            toValue: 0,
            duration: 800,
            easing: Easing.linear,
            useNativeDriver: true
          })
        ])
      ).start();
    } else {
      loadingAnimation.stopAnimation();
    }

    return () => loadingAnimation.stopAnimation();
  }, [isBusDataLoading]);

  // Monitor busStops changes
  useEffect(() => {
    if (busStops && busStops.length > 0) {
      // console.log('Bus stops updated in store:', busStops);
      // Verify the data structure
      if (!busStops.every(stop => stop.stopName && stop.coordinates)) {
        console.warn('Some bus stops are missing required fields');
        // Filter out stops with missing coordinates and log them
        const invalidStops = busStops.filter(stop => !stop.coordinates);
        if (invalidStops.length > 0) {
          console.warn(`Found ${invalidStops.length} stops with missing coordinates:`);
          invalidStops.forEach(stop => {
            console.warn(`- ${stop.stopName} (time: ${stop.time})`);
          });
        }
      }
      // Verify the data structure for valid stops
      const validStops = busStops.filter(stop => stop.coordinates);
      if (!validStops.every(stop => stop.stopName && stop.coordinates)) {
        console.warn('Some valid stops are still missing required fields');
      }
    } else if (busStops) {
      console.warn('Bus stops array is empty in store');
    } else {
      console.warn('Bus stops is undefined in store');
    }
  }, [busStops]);

  // Add this near the top of your component, after the state declarations
  useEffect(() => {
    // Request notification permissions when component mounts
    const requestNotificationPermissions = async () => {
      if (Device.isDevice) {
        const { status: existingStatus } = await Notifications.getPermissionsAsync();
        let finalStatus = existingStatus;

        if (existingStatus !== 'granted') {
          const { status } = await Notifications.requestPermissionsAsync();
          finalStatus = status;
        }

        if (finalStatus !== 'granted') {
          Alert.alert(
            t('tracking.alerts.permissionDeniedTitle'),
            t('tracking.alerts.enableNotificationPermission')
          );
          return false;
        }
        return true;
      }
      return false;
    };

    requestNotificationPermissions();

    // Configure notification handler
    Notifications.setNotificationHandler({
      handleNotification: async () => ({
        shouldShowAlert: true,
        shouldPlaySound: false,
        shouldSetBadge: false,
      }),
    });
  }, []);

  // Add this function to dynamically determine font size based on language
  const getButtonTextStyle = () => {
    const currentLanguage = i18n.language;
    const baseStyle = styles.startButtonText;

    // Create a new style object with adjusted font size for specific languages
    const adjustedStyle = {
      ...baseStyle,
      fontSize: currentLanguage === 'ta' ? 20 : 30, // Smaller font for Tamil
    };

    return adjustedStyle;
  };
  // Add this function to handle dropdown state changes
  const handleDropdownToggle = (isOpen) => {
    setIsDropdownOpen(isOpen);
  };
  const unpressedShadow = {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 4,
  };

  const pressedShadow = {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.4,
    shadowRadius: 8,
    elevation: 6,
  };

  const onPressIn = () => {
    if (isStarted) return;
    setIsPressing(true);
    Animated.timing(pressAnim, {
      toValue: 0.9,
      duration: 100,
      useNativeDriver: true,
    }).start();
  };

  const onPressOut = () => {
    if (isStarted) return;
    Animated.sequence([
      Animated.timing(pressAnim, {
        toValue: 1.02,
        duration: 50,
        useNativeDriver: true,
      }),
      Animated.timing(pressAnim, {
        toValue: 1,
        duration: 50,
        useNativeDriver: true,
      }),
    ]).start(() => setIsPressing(false));
  };
  // Check for existing history or current coordinates when component mounts
  // useEffect(() => {
  //   const checkBusHistory = async () => {
  //     try {
  //       if (busNumber) {
  //         // Get bus history from DynamoDB
  //         const busData = await getBusHistory(busNumber);
  //         const hasHistoryData = busData && busData.history && busData.history.length > 0;
  //         // Store history state
  //         setHasHistory(hasHistoryData);
  //         // Check if there are current coordinates for this bus
  //         const currentCoords = await getBusCurrentCoordinates(busNumber);
  //         const hasCoords = currentCoords &&
  //           currentCoords.latitude &&
  //           currentCoords.longitude;

  //         // Set flag if current coordinates exist
  //         setHasCurrentCoordinates(hasCoords);

  //         // Show resume modal if:
  //         // 1. We have history (meaning we've reached stops) OR
  //         // 2. We have current coordinates but no history (journey just started)
  //         if (hasHistory || hasCoords) {
  //           setShowResumeModal(true);
  //         }
  //       }
  //     } catch (error) {
  //       console.error('Error checking bus history or coordinates:', error);
  //     }
  //   };

  //   checkBusHistory();

  //   // Listen for the lastStopReached event
  //   const lastStopListener = EventRegister.addEventListener('lastStopReached', (receivedBusNumber) => {
  //     console.log(`Received lastStopReached event for bus ${receivedBusNumber}, current bus: ${busNumber}`);

  //     // Verify this is for our bus
  //     if (receivedBusNumber === busNumber) {
  //       console.log('Showing LastStopModal');
  //       setShowLastStopModal(true);
  //     }
  //   });

  //   // Clean up the event listener when component unmounts
  //   return () => {
  //     EventRegister.removeEventListener(lastStopListener);
  //   };
  // }, [busNumber]);

  // Handle resuming the ride
  const handleResumeRide = () => {
    setShowResumeModal(false);

    // Show starting animation
    setIsStarting(true);

    // Start tracking and set bus as active
    setIsStarted(true);
    startTracking();

    // Update driver and bus status to Active
    const updateStatus = async () => {
      try {
        const phoneNumber = route.params?.phoneNumber || await AsyncStorage.getItem('phoneNumber');

        if (phoneNumber && busNumber) {
          await updateDriverStatus(phoneNumber, 'Active');
          await updateBusStatus(busNumber, 'Active');
        }
      } catch (error) {
        console.error('Error updating status:', error);
      }
    };

    updateStatus();

    // Hide the starting animation after 15 seconds
    setTimeout(() => {
      setIsStarting(false);
    }, 15000);
  };


  useEffect(() => {
    let interval = null;
    if (isStarted) {
      interval = setInterval(() => {
        // Use a more unique identifier by combining timestamp with a random number
        setWaves(prev => [...prev, `${Date.now()}-${Math.random().toString(36).substr(2, 5)}`]);
      }, 1500);
    }
    return () => {
      if (interval) clearInterval(interval);
      // Don't clear coordinates when app is closed
      stopTracking(false);
    };
  }, [isStarted]);

  const removeWave = key => {
    setWaves(prev => prev.filter(w => w !== key));
  };

  const handleStopPress = () => {
    setShowStopModal(true);
  };

  const confirmStopRide = async () => {
    // Set stopping state to true at the beginning
    setIsStopping(true);
    console.log("Starting stop ride process...");

    try {
      console.log("1. Stopping tracking and clearing coordinates");
      // First stop tracking and clear coordinates (true = clear coordinates)
      await stopTracking(true);

      // Update driver and bus status to Inactive
      const phoneNumber = await AsyncStorage.getItem('phoneNumber');

      if (phoneNumber && busNumber) {
        console.log(`2. Updating driver ${phoneNumber} status to Inactive`);
        await updateDriverStatus(phoneNumber, 'Inactive');

        console.log(`3. Updating bus ${busNumber} status to Inactive`);
        await updateBusStatus(busNumber, 'Inactive');

        console.log(`4. Resetting bus delay status for ${busNumber}`);
        // Reset bus delay status to false
        await updateBusDelayStatus(busNumber, false);

        console.log(`5. Clearing history for bus ${busNumber}`);
        // Clear the bus history and current coordinates
        await clearBusHistory(busNumber);

        console.log(`6. Clearing coordinates for bus ${busNumber}`);
        // Explicitly clear coordinates to ensure they're gone
        await clearBusCoordinates(busNumber);

        // Clear history from AsyncStorage - only history data
        try {
          console.log(`7. Clearing history from AsyncStorage for bus ${busNumber}`);
          // Get current bus data from AsyncStorage
          const busDataKey = `bus_data_${busNumber}`;
          const cachedData = await AsyncStorage.getItem(busDataKey);

          if (cachedData) {
            const parsedData = JSON.parse(cachedData);

            // Clear only history while preserving other data
            const updatedData = {
              ...parsedData,
              history: [],
              timestamp: Date.now()
            };

            await AsyncStorage.setItem(busDataKey, JSON.stringify(updatedData));
          }

          // Also clear the separate history storage
          await AsyncStorage.removeItem(`busHistory_${busNumber}`);

          console.log(`History for bus ${busNumber} cleared from AsyncStorage`);
        } catch (storageError) {
          console.error('Error clearing history from AsyncStorage:', storageError);
          throw storageError; // Rethrow to handle in outer catch
        }
      } else {
        console.error('Missing phone number or bus number required for cleanup');
        throw new Error('Missing phone number or bus number');
      }

      // Only update UI states after all operations are complete
      console.log("8. All operations completed successfully, updating UI");
      setWaves([]);
      setIsStarted(false);

      // Close the resume modal if it's open
      setShowResumeModal(false);

      console.log("Stop ride process completed successfully");
    } catch (error) {
      console.error('Error in confirmStopRide:', error);
      Alert.alert(
        t('tracking.alerts.errorTitle'),
        t('tracking.alerts.errorStoppingRide')
      );
      throw error; // Rethrow to ensure the finally block executes after any promise chain
    } finally {
      // Set stopping state to false at the end
      setIsStopping(false);
    }
  };

  // Inside the TrackingScreen component, modify the startTracking function:
  const startTracking = async () => {
    try {
      // Reset delay tracking variables
      lastCoordinates = null;
      stationaryStartTime = null;

      // Check if location services are enabled
      const servicesEnabled = await requestLocationServicesEnabled();
      if (!servicesEnabled) {
        return;
      }

      // Request permissions
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(t('tracking.alerts.permissionDeniedTitle'),
          t('tracking.alerts.enableLocationPermission'));
        return;
      }

      const { status: bgStatus } = await Location.requestBackgroundPermissionsAsync();
      if (bgStatus !== 'granted') {
        Alert.alert(t('tracking.alerts.permissionDeniedTitle'),
          t('tracking.alerts.enableBackgroundPermission'));
        return;
      }

      // Wait for GPS to warm up and achieve good accuracy
      Alert.alert('Initializing GPS', 'Please wait while we get a precise location fix...');

      let accurateLocation = false;
      let retryCount = 0;
      while (!accurateLocation && retryCount < 3) {
        accurateLocation = await checkLocationAccuracy();
        if (!accurateLocation) {
          await new Promise(resolve => setTimeout(resolve, 2000));
          retryCount++;
        }
      }

      if (!accurateLocation) {
        Alert.alert('GPS Signal Weak',
          'Please ensure you are outdoors or near windows for better GPS signal');
      }

      // Add initial location check with fallback
      let initialLocation = null;
      retryCount = 0;

      while (!initialLocation && retryCount < MAX_RETRIES) {
        try {
          initialLocation = await Location.getCurrentPositionAsync({
            accuracy: Location.Accuracy.BestForNavigation,
          });
        } catch (error) {
          console.log(`Initial location attempt ${retryCount + 1} failed:`, error);
          await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));
        }
        retryCount++;
      }

      if (!initialLocation) {
        initialLocation = await getLastKnownLocation();
        if (initialLocation) {
          console.log('Using last known location for initial position');
        } else {
          console.warn('No location available, starting without initial position');
        }
      }

      if (initialLocation?.coords) {
        await AsyncStorage.setItem('lastKnownLocation', JSON.stringify(initialLocation));
      }

      // Start location tracking with improved settings
      await Location.startLocationUpdatesAsync('BACKGROUND_LOCATION_TASK', {
        accuracy: Location.Accuracy.BestForNavigation,
        timeInterval: 10000,
        distanceInterval: 0,
        // Ensure GPS provider is preferred
        foregroundService: {
          notificationTitle: t('tracking.notification.title'),
          notificationBody: t('tracking.notification.body', { busNumber: busNumber }),
          notificationColor: "#00FF00",
          notificationPriority: "max",
          killServiceOnDestroy: false,
        },
        // Add Android specific options
        android: {
          // Force usage of GPS provider
          forceLocationManager: true,
          // Ensure GPS is used even in battery saver mode
          foregroundService: {
            importance: 3, // IMPORTANCE_HIGH
            enableVibrate: false,
            enableWakeLock: true
          }
        }
      });

      // Log the bus number from route params
      console.log('Bus number from route params:', busNumber);

      // Store busNumber in AsyncStorage before starting location updates
      if (busNumber) {
        await AsyncStorage.setItem('busNumber', busNumber);
        console.log('Bus number stored in AsyncStorage:', busNumber);

        // Verify it was stored correctly
        const storedBusNumber = await AsyncStorage.getItem('busNumber');
        console.log('Verified bus number from AsyncStorage:', storedBusNumber);
      } else {
        console.error('Bus number is undefined or null, cannot store in AsyncStorage');
      }

      // Get phone number from AsyncStorage with verification
      const phoneNumber = await AsyncStorage.getItem('phoneNumber');
      console.log('Phone number from AsyncStorage:', phoneNumber);

      if (!phoneNumber) {
        console.error('Phone number not found in AsyncStorage');
        // Try to get it from route params as fallback
        const routePhoneNumber = route.params?.phoneNumber;
        if (routePhoneNumber) {
          console.log('Using phone number from route params:', routePhoneNumber);
          await AsyncStorage.setItem('phoneNumber', routePhoneNumber);
          console.log('Phone number stored in AsyncStorage from route params');
        } else {
          console.error('No phone number available from any source');
        }
      }

      // Final verification of both values
      const finalBusNumber = await AsyncStorage.getItem('busNumber');
      const finalPhoneNumber = await AsyncStorage.getItem('phoneNumber');

      console.log('Final verification - Bus number:', finalBusNumber);
      console.log('Final verification - Phone number:', finalPhoneNumber);

      // Assign bus to driver in DynamoDB only if both values are available
      if (finalPhoneNumber && finalBusNumber) {
        try {
          console.log(`Attempting to update driver status: ${finalPhoneNumber} to Active`);
          const driverResult = await updateDriverStatus(finalPhoneNumber, 'Active');
          console.log('Driver status update result:', driverResult);

          console.log(`Attempting to update bus status: ${finalBusNumber} to Active`);
          const busResult = await updateBusStatus(finalBusNumber, 'Active');
          console.log('Bus status update result:', busResult);

          console.log(`Attempting to assign bus ${finalBusNumber} to driver ${finalPhoneNumber}`);
          await updateDriverAssignedBus(finalPhoneNumber, finalBusNumber);
          console.log(`Bus ${finalBusNumber} assigned to driver ${finalPhoneNumber}`);
        } catch (assignError) {
          console.error('Error updating status or assigning bus to driver:', assignError);
        }
      } else {
        console.error('Cannot update status: Missing bus number or phone number');
        Alert.alert(t('tracking.alerts.errorTitle'), t('tracking.alerts.missingInfo'));
      }


      // Start location tracking
      await Location.startLocationUpdatesAsync('BACKGROUND_LOCATION_TASK', {
        accuracy: Location.Accuracy.Highest,
        timeInterval: 10000,
        distanceInterval: 0, // Changed from 10 to 0 to update regardless of distance moved
        deferredUpdatesInterval: 0, // Ensure immediate updates
        deferredUpdatesDistance: 0, // Ensure immediate updates
        showsBackgroundLocationIndicator: true,
        foregroundService: {
          notificationTitle: t('tracking.notification.title'),
          notificationBody: t('tracking.notification.body', { busNumber: busNumber }),
          notificationColor: "#00FF00",
          notificationPriority: "max", // Add high priority
          killServiceOnDestroy: false, // Prevent service from being killed
        }
      });

      console.log('Location tracking started.');

      // If the bus was previously marked as delayed, reset it

      // --- Add Persistent Notification Scheduling ---
      // Schedule a separate persistent notification using expo-notifications
      const scheduledNotificationId = await Notifications.scheduleNotificationAsync({
        content: {
          title: t('tracking.notification.title'),
          body: t('tracking.notification.body', { busNumber: busNumber }),
          data: { busNumber },
          sticky: true, // Make it persistent
          autoDismiss: false, // Prevent auto dismissal
          priority: 'max', // High priority
          notificationChannelId: 'tracking-channel', // Use the dedicated channel (Android)
          // You might want a different icon or color for this one to distinguish it,
          // but for now, let's keep it similar for testing persistence.
          // color: "#007bff", // Example: different color
          // icon: require('../assets/images/icon.png'), // Example: different icon
        },
        trigger: null, // Show immediately
      });

      console.log('Persistent notification scheduled with ID:', scheduledNotificationId);
      setNotificationId(scheduledNotificationId); // Store the ID in state
      // --- End Persistent Notification Scheduling ---

      try {
        const finalBusNumber = await AsyncStorage.getItem('busNumber');
        if (finalBusNumber) {
          await updateBusDelayStatus(finalBusNumber, false);
        }
      } catch (error) {
        console.error('Error resetting bus delay status:', error);
      }
      // Schedule the ongoing ride notification
      // await scheduleOngoingRideNotification();
      // Create a persistent notification to keep the service alive
      // await createPersistentNotification(busNumber);
    } catch (error) {
      console.log('Error starting location tracking', error);
      Alert.alert(t('tracking.alerts.errorTitle'), t('tracking.alerts.unableToStartTracking'));
    }
  };

  // // Add this new function to create a persistent notification
  // const createPersistentNotification = async (busNumber) => {
  //   try {
  //     // Cancel any existing notifications first
  //     await Notifications.dismissAllNotificationsAsync();

  //     // Create a notification channel for Android (required for persistent notifications)
  //     if (Platform.OS === 'android') {
  //       await Notifications.setNotificationChannelAsync('tracking-service', {
  //         name: 'Tracking Service',
  //         importance: Notifications.AndroidImportance.MAX,
  //         vibrationPattern: [0, 250, 250, 250],
  //         lightColor: '#FF0000',
  //       });
  //     }

  //     // Schedule a persistent notification
  //     const notificationId = await Notifications.scheduleNotificationAsync({
  //       content: {
  //         title: t('tracking.notification.title'),
  //         body: t('tracking.notification.body', { busNumber: busNumber }),
  //         data: { busNumber },
  //         sticky: true, // Make it persistent
  //         autoDismiss: false, // Prevent auto dismissal
  //         priority: 'max',
  //       },
  //       trigger: null, // null means show immediately
  //     });

  //     console.log('Created persistent notification with ID:', notificationId);
  //     setNotificationId(notificationId);

  //     return notificationId;
  //   } catch (error) {
  //     console.error('Error creating persistent notification:', error);
  //     return null;
  //   }
  // };

  const stopTracking = async (clearCoords = false) => {
    console.log("Stopping tracking process started");
    // Reset delay tracking variables
    lastCoordinates = null;
    stationaryStartTime = null;

    // First try to stop location updates
    let locationStopped = false;
    try {
      // Check if the task exists before trying to stop it
      const tasks = await TaskManager.getRegisteredTasksAsync();
      const taskExists = tasks.some(task => task.taskName === 'BACKGROUND_LOCATION_TASK');

      if (taskExists) {
        await Location.stopLocationUpdatesAsync('BACKGROUND_LOCATION_TASK');
        console.log('Location tracking successfully stopped');
        locationStopped = true;
      } else {
        console.log('No active location tracking task found - already stopped');
        locationStopped = true; // Consider it stopped
      }

      // Only clear coordinates if explicitly requested
      if (clearCoords && busNumber) {
        await clearBusCoordinates(busNumber);
        console.log('Bus coordinates cleared as requested');
      } else {
        console.log('Keeping bus coordinates for resume functionality');
      }
    } catch (error) {
      // Check if the error is specifically about the task not being found
      if (error.message && error.message.includes("Task 'BACKGROUND_LOCATION_TASK' not found")) {
        locationStopped = true;
      } else {
        console.error('Error stopping location tracking:', error);
      }
      locationStopped = true; // Consider it stopped even if there's an error
    }

    // If we're stopping tracking, make sure to reset the delay status
    try {
      if (busNumber) {
        await updateBusDelayStatus(busNumber, false);
      }
    } catch (error) {
      console.error('Error resetting bus delay status on stop:', error);
    }
    // Try multiple approaches to cancel notifications
    // Try multiple approaches to cancel notifications
    // try {
    //   // First try to cancel the specific notification
    //   if (notificationId) {
    //     await Notifications.cancelScheduledNotificationAsync(notificationId);
    //     console.log(`Canceled specific notification with ID: ${notificationId}`);
    //   }

    //   // Then try to dismiss all notifications
    //   await Notifications.dismissAllNotificationsAsync();
    //   console.log('Dismissed all notifications');

    //   // Also cancel all scheduled notifications
    //   await Notifications.cancelAllScheduledNotificationsAsync();
    //   console.log('Canceled all scheduled notifications');

    //   // Reset notification ID
    //   setNotificationId(null);
    // } catch (error) {
    //   console.error('Error canceling notifications:', error);
    // }
    // --- Add Notification Cancellation ---
    // Cancel the manually scheduled persistent notification
    try {
      if (notificationId) { // Check if we have a stored notification ID
        await Notifications.cancelScheduledNotificationAsync(notificationId);
        console.log(`Canceled specific persistent notification with ID: ${notificationId}`);
      } else {
        // Fallback: try dismissing all if ID wasn't stored for some reason
        // This might dismiss the foreground service notification too, which is fine on stop.
        await Notifications.dismissAllNotificationsAsync();
        console.log('No specific notification ID, dismissed all notifications');
      }
      setNotificationId(null); // Clear the stored ID
    } catch (error) {
      console.error('Error canceling persistent notification:', error);
    }
    // --- End Notification Cancellation ---
    // If we're stopping tracking, make sure to reset the delay status
    try {
      if (busNumber) {
        await updateBusDelayStatus(busNumber, false);
      }
    } catch (error) {
      console.error('Error resetting bus delay status on stop:', error);
    }
    return locationStopped;
  };


  // Handle "End Journey" button in last stop modal - modified to clear history
  const handleLastStopEndJourney = async () => {
    try {
      if (busNumber) {

        setIsStarted(false);
        setWaves([]);
        // await cancelOngoingRideNotification();
        await stopTracking(true);

        // Update driver status to Inactive
        const phoneNumber = await AsyncStorage.getItem('phoneNumber');
        if (phoneNumber) {
          await updateDriverStatus(phoneNumber, 'Inactive');
        }
        // Explicitly clear bus history
        await clearBusHistory(busNumber);

        // Also update bus status to Inactive
        await updateBusStatus(busNumber, 'Inactive');
        // Reset bus delay status to false
        await updateBusDelayStatus(busNumber, false);
        console.log(`Journey ended and history cleared for bus ${busNumber}`);
      }
    } catch (error) {
      console.error('Error ending journey:', error);
    }

    setShowLastStopModal(false);
  };

  // Handle "Start New Journey" button in last stop modal
  const handleLastStopNewJourney = async () => {
    try {
      if (busNumber) {
        // First stop tracking and clear history
        setIsStarted(false);
        setWaves([]);
        // await cancelOngoingRideNotification();
        await stopTracking(true);

        // Clear the bus history
        await clearBusHistory(busNumber);

        // Show starting animation
        setIsStarting(true);

        // Then start tracking again
        setIsStarted(true);
        startTracking();

        // Update driver and bus status to Active
        const phoneNumber = await AsyncStorage.getItem('phoneNumber');
        if (phoneNumber) {
          await updateDriverStatus(phoneNumber, 'Active');
          await updateBusStatus(busNumber, 'Active');
        }

        // Hide the starting animation after 15 seconds
        setTimeout(() => {
          setIsStarting(false);
        }, 15000);
      }
    } catch (error) {
      console.error('Error starting new journey:', error);
    }

    setShowLastStopModal(false);
  };
  // Update the handler function for the dropdown menu
  const handleUpdatePress = async () => {
    try {
      console.log('Release notes button pressed - Starting handleUpdatePress');

      // // Get current version
      // const currentVersion = getCurrentVersion();
      // console.log('Current version:', currentVersion);
      // Import and use the release notes from the constants file
      const { CURRENT_RELEASE_NOTES } = require('../constants/ReleaseNotes');
      console.log('Release notes imported:', CURRENT_RELEASE_NOTES);

      // Format the notes as a bulleted list
      const formattedNotes = CURRENT_RELEASE_NOTES.notes.map(note => `• ${note}`).join('\n');
      console.log('Formatted release notes:', formattedNotes);
      // // Emit event to show the release notes modal
      // EventRegister.emit('showReleaseNotes', {
      //   version: CURRENT_RELEASE_NOTES.version || currentVersion,
      //   date: CURRENT_RELEASE_NOTES.date,
      //   notes: formattedNotes
      // });
      // Set modal state and content
      console.log('Setting modal state and content...');
      setReleaseNotesVersion(CURRENT_RELEASE_NOTES.version || currentVersion);
      setReleaseNotesDate(CURRENT_RELEASE_NOTES.date);
      setReleaseNotesContent(formattedNotes);
      setShowReleaseNotesModal(true);

      console.log('Release notes modal should now be visible');

    } catch (error) {
      console.error('Error showing release notes:', error);
      Alert.alert(t('tracking.alerts.errorTitle'), t('tracking.alerts.failedReleaseNotes'));
    }
  };

  const handleFuelDataPress = () => {
    Alert.alert(t('tracking.comingSoon.title'), t('tracking.comingSoon.fuelData'));
  };

  const handleSettingsPress = () => {
    Alert.alert(t('tracking.comingSoon.title'), t('tracking.comingSoon.settings'));
  };

  const handleProfilePress = () => {
    Alert.alert(t('tracking.comingSoon.title'), t('tracking.comingSoon.profile'));
  };

  // Add focus effect to check tracking state when returning to screen
  useFocusEffect(
    React.useCallback(() => {
      // Check if location tracking is still active when screen comes into focus
      const checkTrackingStatus = async () => {
        try {
          const hasStartedLocation = await Location.hasStartedLocationUpdatesAsync('BACKGROUND_LOCATION_TASK');
          console.log('Location tracking status on focus:', hasStartedLocation);

          // Update the UI state to match the actual tracking status
          if (hasStartedLocation && !isStarted) {
            console.log('Tracking is active but UI shows stopped - updating UI');
            setIsStarted(true);
          } else if (!hasStartedLocation && isStarted) {
            console.log('Tracking is stopped but UI shows active - updating UI');
            setIsStarted(false);
          }
        } catch (error) {
          console.error('Error checking tracking status:', error);
        }
      };

      checkTrackingStatus();
    }, [isStarted])
  );

  // Modify this useEffect to handle app state changes correctly
  useEffect(() => {
    // Subscribe to AppState changes
    const subscription = AppState.addEventListener('change', nextAppState => {
      console.log(`App state changed from ${appState.current} to ${nextAppState}`);

      // Only set inactive when app is terminated, not when it goes to background
      if (appState.current === 'active' && nextAppState === 'inactive') {
        console.log('App is being terminated');

        // Set bus and driver to inactive only when app is terminated
        const setInactiveOnClose = async () => {
          try {
            if (busNumber) {
              const phoneNumber = await AsyncStorage.getItem('phoneNumber');
              if (phoneNumber) {
                console.log(`Setting bus ${busNumber} and driver ${phoneNumber} to inactive on app termination`);
                await updateDriverStatus(phoneNumber, 'Inactive');
                await updateBusStatus(busNumber, 'Inactive');
                // Unassign the driver from the bus
                await updateDriverAssignedBus(phoneNumber, "Unassigned");
                console.log(`Unassigned bus from driver ${phoneNumber} on app termination`);
                // Remove busNumber and phoneNumber from AsyncStorage to force redirect to Number screen
                await AsyncStorage.removeItem('busNumber');
                await AsyncStorage.removeItem('phoneNumber');
                console.log('Removed busNumber and phoneNumber from AsyncStorage for redirect on next launch');
              }
            }
          } catch (error) {
            console.error('Error setting inactive status on app termination:', error);
          }
        };

        setInactiveOnClose();
      }
      // When app comes back to foreground from background, ensure status is Active if tracking
      else if (appState.current === 'background' && nextAppState === 'active' && isStarted) {
        console.log('App returning to foreground, ensuring active status');

        const ensureActiveStatus = async () => {
          try {
            if (busNumber) {
              const phoneNumber = await AsyncStorage.getItem('phoneNumber');
              if (phoneNumber) {
                console.log(`Ensuring bus ${busNumber} and driver ${phoneNumber} are Active on return to foreground`);
                await updateDriverStatus(phoneNumber, 'Active');
                await updateBusStatus(busNumber, 'Active');
              }
            }
          } catch (error) {
            console.error('Error ensuring active status on return to foreground:', error);
          }
        };

        ensureActiveStatus();
      }

      // Update the appState ref
      appState.current = nextAppState;
    });

    // Clean up on component unmount
    return () => {
      subscription.remove();
    };
  }, [busNumber, isStarted]);
  // Clean up notification when component unmounts
  useEffect(() => {
    return () => {
      // Also set inactive when component unmounts
      const cleanupOnUnmount = async () => {
        try {
          if (busNumber) {
            const phoneNumber = await AsyncStorage.getItem('phoneNumber');
            if (phoneNumber) {
              console.log(`Setting bus ${busNumber} and driver ${phoneNumber} to inactive on unmount`);
              await updateDriverStatus(phoneNumber, 'Inactive');
              await updateBusStatus(busNumber, 'Inactive');
              // Unassign the driver from the bus
              await updateDriverAssignedBus(phoneNumber, "Unassigned");
              console.log(`Unassigned bus from driver ${phoneNumber} on component unmount`)
            }
          }
        } catch (error) {
          console.error('Error setting inactive status on unmount:', error);
        }
      };

      cleanupOnUnmount();
    };
  }, [busNumber]);

  // Add rotation animation for stopping overlay
  useEffect(() => {
    if (isStopping || isStarting) {
      Animated.loop(
        Animated.timing(rotateAnimation, {
          toValue: 1,
          duration: 1000,
          easing: Easing.linear,
          useNativeDriver: true
        })
      ).start();
    } else {
      rotateAnimation.setValue(0);
    }

    return () => {
      rotateAnimation.setValue(0);
    };
  }, [isStopping, isStarting]);

  return (
    <View style={styles.container}>
      {/* Stopping overlay - blocks all user interaction */}
      {isStopping && (
        <View style={styles.stoppingOverlay}>
          <View style={styles.loadingContainer}>
            <Animated.View style={[
              styles.loadingIndicator,
              { opacity: 0.8 }
            ]}>
              <Animated.View
                style={{
                  transform: [{
                    rotate: rotateAnimation.interpolate({
                      inputRange: [0, 1],
                      outputRange: ['0deg', '360deg']
                    })
                  }]
                }}
              >
                <FontAwesome6
                  name="circle-notch"
                  size={40}
                  color="#FFFFFF"
                />
              </Animated.View>
            </Animated.View>
            <Text style={styles.loadingText}>{t('tracking.stoppingRide')}</Text>
          </View>
        </View>
      )}

      {/* Starting overlay - blocks all user interaction */}
      {isStarting && (
        <View style={styles.stoppingOverlay}>
          <View style={styles.loadingContainer}>
            <Animated.View style={[
              styles.loadingIndicator,
              { opacity: 0.8 }
            ]}>
              <Animated.View
                style={{
                  transform: [{
                    rotate: rotateAnimation.interpolate({
                      inputRange: [0, 1],
                      outputRange: ['0deg', '360deg']
                    })
                  }]
                }}
              >
                <FontAwesome6
                  name="circle-notch"
                  size={40}
                  color="#FFFFFF"
                />
              </Animated.View>
            </Animated.View>
            <Text style={styles.loadingText}>{t('tracking.startingRide')}</Text>
          </View>
        </View>
      )}

      {/* Loading Overlay */}
      {isBusDataLoading && (
        <View style={styles.loadingOverlay}>
          <View style={styles.loadingContainer}>
            <Animated.View style={[
              styles.loadingIndicator,
              {
                opacity: loadingAnimation,
                transform: [{
                  scale: loadingAnimation.interpolate({
                    inputRange: [0, 1],
                    outputRange: [0.8, 1.2]
                  })
                }]
              }
            ]}>
              <FontAwesome6
                name="bus"
                size={40}
                color={mode === 'light' ? '#FFFFFF' : theme.busIconColor}
              />
            </Animated.View>
            <Text style={styles.loadingText}>{t('tracking.loading.busData')}</Text>
          </View>
        </View>
      )}
      {isDropdownOpen && (
        <TouchableOpacity
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            width: '100%',
            height: '100%',
            zIndex: 999,
          }}
          activeOpacity={0}
          onPress={() => setIsDropdownOpen(false)}
        />
      )}
      <ImageBackground
        source={mode === 'dark' ? MapsImageDark : MapsImage}
        style={styles.backgroundImage}
        resizeMode="cover"
        imageStyle={styles.backgroundImageStyle}
      >

        {/* Modified DropdownMenu with isOpen state and onToggle callback */}
        <DropdownMenu
          isOpen={isDropdownOpen}
          onToggle={handleDropdownToggle}
          onUpdatePress={handleUpdatePress}
          onFuelDataPress={handleFuelDataPress}
          onSettingsPress={handleSettingsPress}
          onProfilePress={handleProfilePress}
        />

        {/* Header Card */}
        <View style={styles.headerCard}>
          <View style={styles.headerTopRow}>
            <FontAwesome6
              name="bus"
              size={40}
              color={theme.busIconColor}
              style={styles.trainIcon}
            />
            <Text style={styles.busNumber}>{busNumber}</Text>
          </View>
          <View style={styles.separator} />
          <View style={styles.routeRow}>
            <View style={styles.routeCircleContainer}>
              <View style={styles.routeCircle}>
                <Text style={styles.routeLabel}>Mdy</Text>
              </View>
              <Text style={styles.routeSubtitle}>{t('tracking.route.mayiladuthurai')}</Text>
            </View>
            <View style={[styles.dottedLine]} />
            <View style={styles.routeCircleContainer}>
              <View style={styles.routeCircle}>
                <Text style={styles.routeLabel}>KKL</Text>
              </View>
              <Text style={styles.routeSubtitle}>{t('tracking.route.karaikal')}</Text>
            </View>
          </View>
        </View>

        {/* Combined Header Buttons */}
        <View style={styles.headerButtonsContainer}>
          <LogoutButton />
          <GPSStatusIndicator />
          <ColorModeToggle />
        </View>

        {/* Main Content */}
        <View style={styles.mainContent}>
          <View style={{ position: 'relative', alignItems: 'center' }}>
            {/* Move RadarWave behind the start button */}
            {waves.map(key => (
              <RadarWave key={key} onComplete={() => removeWave(key)} />
            ))}

            <Animated.View
              style={[{ transform: [{ scale: pressAnim }] }, isPressing ? pressedShadow : unpressedShadow]}
            >
              <TouchableOpacity
                activeOpacity={0.8}
                onPressIn={onPressIn}
                onPressOut={onPressOut}
                // Inside the TouchableOpacity onPress handler:

                onPress={async () => {
                  if (!isStarted) {
                    console.log("Bus started button pressed");
                    if (Device.isDevice) {
                      const { status: existingStatus } = await Notifications.getPermissionsAsync();
                      let finalStatus = existingStatus;

                      if (existingStatus !== 'granted') {
                        const { status } = await Notifications.requestPermissionsAsync();
                        finalStatus = status;
                      }

                      if (finalStatus !== 'granted') {
                        Alert.alert(
                          t('tracking.alerts.permissionDeniedTitle'),
                          t('tracking.alerts.enableNotificationPermission'),
                          [
                            {
                              text: t('common.cancel'),
                              style: 'cancel'
                            },
                            {
                              text: t('common.settings'),
                              onPress: () => {
                                Linking.openSettings();
                              }
                            }
                          ]
                        );
                        return; // Exit if permission not granted
                      }
                      console.log('Notification permission granted, proceeding with tracking');
                    }

                    // Show starting animation
                    setIsStarting(true);

                    // Set state to started immediately
                    setIsStarted(true);

                    // Update driver and bus status to Active first
                    try {
                      // Get phone number from route params or AsyncStorage
                      let phoneNumber = route.params?.phoneNumber;
                      console.log('Phone number from route params:', phoneNumber);

                      if (!phoneNumber) {
                        phoneNumber = await AsyncStorage.getItem('phoneNumber');
                        console.log('Phone number from AsyncStorage:', phoneNumber);
                      }

                      // Verify bus number
                      console.log('Bus number from route params:', busNumber);
                      const storedBusNumber = await AsyncStorage.getItem('busNumber');
                      console.log('Bus number from AsyncStorage:', storedBusNumber);

                      // Use the most reliable source for bus number
                      const finalBusNumber = busNumber || storedBusNumber;

                      if (phoneNumber && finalBusNumber) {
                        console.log(`Setting bus ${finalBusNumber} and driver ${phoneNumber} to Active on button press`);

                        // Store both values in AsyncStorage to ensure consistency
                        await AsyncStorage.setItem('phoneNumber', phoneNumber);
                        await AsyncStorage.setItem('busNumber', finalBusNumber);

                        // Update status immediately
                        await updateDriverStatus(phoneNumber, 'Active');
                        await updateBusStatus(finalBusNumber, 'Active');
                      } else {
                        console.error('Missing phone number or bus number for status update');
                        if (!phoneNumber) console.error('Phone number is missing');
                        if (!finalBusNumber) console.error('Bus number is missing');
                      }
                    } catch (error) {
                      console.error('Error updating status on button press:', error);
                    }

                    // Start tracking
                    startTracking();

                    // Hide the starting animation after 15 seconds
                    setTimeout(() => {
                      setIsStarting(false);
                    }, 15000);
                  }
                }}
                disabled={isStarted}
              >
                <LinearGradient
                  colors={theme.startButtonColor}
                  start={{ x: 0.5, y: 0 }}
                  end={{ x: 0.5, y: 1 }}
                  style={styles.startButton}
                >
                  {isStopping ? (
                    // Use the LoadingIndicator component instead of inline code
                    <LoadingIndicator
                      message={t('tracking.stoppingRide')}
                      color={theme.normal_text}
                      containerStyle={styles.loadingContainerInButton}
                    />
                  ) : (
                    // Show normal button text
                    <Text style={getButtonTextStyle()}>
                      {isStarted ? t('tracking.stopRide') : t('tracking.startRide')}
                    </Text>
                  )}
                </LinearGradient>
              </TouchableOpacity>
            </Animated.View>

            {isStarted && (
              <TouchableOpacity
                onPress={handleStopPress}
                style={styles.stopIconAbsolute}
              >
                <FontAwesome6 name="xmark-circle" size={50} color={theme.XIconColor} />
              </TouchableOpacity>
            )}
          </View>
          {/* Change test button to open StopRideModal instead of LastStopModal */}
          {/* <TouchableOpacity
            style={styles.testButton}
            onPress={() => setShowLastStopModal(true)}
          >
            <Text style={styles.testButtonText}>Test Last Stop Modal</Text>
          </TouchableOpacity> */}
        </View>

        {/* Print Ticket Button */}
        <View style={styles.printTicketContainer}>
          <TouchableOpacity
            style={isStarted ? styles.printTicketButton : styles.printTicketButtonDisabled}
            onPress={() => navigation.navigate('Ticket', { busNumber })}
            disabled={!isStarted}
          >
            {isStarted ? (
              <Text style={styles.printTicketText}>{t('tracking.printTicket')}</Text>
            ) : (
              <View style={styles.printTicketContent}>
                <FontAwesome6
                  name="circle-exclamation"
                  size={24}
                  color="#888888"
                  style={{ marginRight: 8 }}
                />
                <Text style={styles.printTicketTextDisabled}>
                  {t('tracking.printTicketDisabled')}
                </Text>
              </View>
            )}
          </TouchableOpacity>
        </View>
      </ImageBackground>
      {/* Add the ResumeRideModal component */}
      <ResumeRideModal
        visible={showResumeModal}
        onResume={handleResumeRide}
        onNewRide={confirmStopRide}
        hasCurrentCoordinates={hasCurrentCoordinates}
        hasHistory={hasHistory}
      />
      {/* Add the StopRideModal component with busNumber prop */}
      <StopRideModal
        visible={showStopModal}
        onCancel={() => setShowStopModal(false)}
        onConfirm={() => {
          confirmStopRide();
          setShowStopModal(false);
        }}
        busNumber={busNumber}
      />
      {/* Last Stop Modal - removed onLater prop */}
      <LastStopModal
        visible={showLastStopModal}
        onEndJourney={handleLastStopEndJourney}
        onStartNewJourney={handleLastStopNewJourney}
        busNumber={busNumber}
      />
      <ReleaseNotesModal
        visible={showReleaseNotesModal}
        onClose={() => setShowReleaseNotesModal(false)}
        version={releaseNotesVersion}
        date={releaseNotesDate}
        notes={releaseNotesContent}
      />
    </View>
  );
}

// RadarWave Component for animated waves
function RadarWave({ onComplete }) {
  const { theme } = useContext(ThemeContext);
  const scale = useRef(new Animated.Value(1)).current;
  const opacity = useRef(new Animated.Value(0.5)).current;

  useEffect(() => {
    Animated.parallel([
      Animated.timing(scale, {
        toValue: 3,
        duration: 2000,
        easing: Easing.linear,
        useNativeDriver: true,
      }),
      Animated.timing(opacity, {
        toValue: 0,
        duration: 1000,
        easing: Easing.linear,
        useNativeDriver: true,
      }),
    ]).start(({ finished }) => {
      if (finished && onComplete) {
        onComplete();
      }
    });
  }, [scale, opacity, onComplete]);

  return (
    <Animated.View
      pointerEvents="none"
      style={{
        position: 'absolute',
        width: 200,
        height: 200,
        borderRadius: 100,
        borderWidth: 100,
        borderColor: theme.startButtonColor[0],
        transform: [{ scale }],
        opacity,
      }}
    />
  );
}
