// ReleaseNotes.js - Contains release notes for each app update
// Update this file with each new EAS update

// Current release notes - this will be shown to users after update
export const CURRENT_RELEASE_NOTES = {
  version: "1.0.3",
  date: "25/04/2025",
  notes: [
    "Added real-time GPS signal strength indicator",
    "Enhanced location accuracy with GPS warm-up period",
    "Improved location tracking reliability:",
    "- Added accuracy threshold checks (20m for strong signal)",
    "- Implemented automatic GPS signal quality monitoring",
    "- Added visual feedback for GPS signal strength",
    "- Enhanced error handling for location services",
    "Added location fallback mechanism:",
    "- Automatic retry for failed location updates",
    "- Last known location storage and recovery",
    "- Improved handling of GPS signal loss",
    "Improved UI feedback for location tracking status",
    "Added automatic retry for poor GPS signals",
    "Enhanced battery efficiency for location tracking"
  ]
};

// Optional: History of previous releases for reference
export const RELEASE_HISTORY = [
  {
    version: "1.0.2",
    date: "15/04/2025",
    notes: [
      "Fixed: Improved bus stop sequence and time difference handling",
      "Enhanced logic for checking bus stop history by calculating sequence distance between stops",
      "Adjusted conditions for skipping stops based on both time difference and sequence distance",
      "Prevented out-of-sequence recording while allowing sequential stops with large time differences",
      "Refactor: Improved bus stop history logic with time-based instance selection",
      "Enhanced the logic for determining the most likely bus stop instance using scheduled times",
      "Ensured more accurate tracking, especially for the first entry or when no history is available",
      "Improved logging and avoided duplicate entries by considering stop indices",
      "Added loading animation overlay during bus data fetching",
      "Improved bus data loading logic with better error handling",
      "Enhanced coordinate validation checks",
      "Added detailed logging for debugging purposes",
      "Implemented new translation keys for loading messages",
      "Updated EAS configuration for testing environment",
      "Added internet connectivity monitoring",
      "Implemented No Internet Connection modal with retry and restart options",
      "Improved app stability during network changes",
      "Added multi-language support with translations for English, Hindi, Urdu, and Tamil",
      "Updated all UI components to display text in the selected language",
      "Added language selection option",
      "Implemented confirmation modal requiring bus number verification to end journey or start new trip",
      "Improved UI for journey management with better visual feedback",
      "Fixed background location tracking",
      "Improved battery usage",
      "Enhanced UI responsiveness",
      "Added translation support and dynamic font size adjustment"
    ]
  },
  {
    version: "1.0.1",
    date: "12/04/2025",
    notes: [
      "Added loading animation overlay during bus data fetching",
      "Improved bus data loading logic with better error handling",
      "Enhanced coordinate validation checks",
      "Added detailed logging for debugging purposes",
      "Implemented new translation keys for loading messages",
      "Updated EAS configuration for testing environment",
      "Added internet connectivity monitoring",
      "Implemented No Internet Connection modal with retry and restart options",
      "Improved app stability during network changes",
      "Added multi-language support with translations for English, Hindi, Urdu, and Tamil",
      "Updated all UI components to display text in the selected language",
      "Added language selection option",
      "Implemented confirmation modal requiring bus number verification to end journey or start new trip",
      "Improved UI for journey management with better visual feedback",
      "Fixed background location tracking",
      "Improved battery usage",
      "Enhanced UI responsiveness",
      "Added translation support and dynamic font size adjustment"
    ]
  },
  {
    version: "1.0.0",
    date: "2023-04-01",
    notes: [
      "Added multi-language support with translations for English, Hindi, Urdu, and Tamil",
      "Updated all UI components to display text in the selected language",
      "Added language selection option",
      "Implemented confirmation modal requiring bus number verification to end journey or start new trip",
      "Improved UI for journey management with better visual feedback",
      "Fixed background location tracking",
      "Improved battery usage",
      "Enhanced UI responsiveness"
    ]
  },
  {
    version: "0.9.5",
    date: "2023-10-30",
    notes: [
      "Initial beta release",
      "Basic tracking functionality",
      "Driver status updates"
    ]
  }
];
