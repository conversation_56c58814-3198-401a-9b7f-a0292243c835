# 🚨 Expo SDK 52 Build Fix Guide

## **Problem**: Expo SDK 52 Gradle Plugin Issues

The error you're seeing is a **known issue** with Expo SDK 52 and certain native modules. Here are **3 solutions** in order of recommendation:

---

## 🎯 **Solution 1: Downgrade to Expo SDK 51 (RECOMMENDED)**

Expo SDK 51 is more stable for production builds with native modules.

### **Step 1: Update package.json**
```bash
cd driver-app

# Downgrade to Expo SDK 51
npx expo install expo@~51.0.0

# Fix all dependencies
npx expo install --fix
```

### **Step 2: Update app.json**
```json
{
  "expo": {
    "runtimeVersion": "exposdk:51.0.0"
  }
}
```

### **Step 3: Build**
```bash
eas build --profile development --platform android --clear-cache
```

---

## 🔧 **Solution 2: Use Local Build (FASTEST)**

If EAS builds keep failing, use local builds:

### **Prerequisites:**
- Android Studio installed
- Android SDK configured

### **Commands:**
```bash
cd driver-app

# Install dependencies
npm install

# Create local development build
npx expo run:android

# This will:
# - Generate Android project
# - Build and install on connected device
# - Bypass EAS build issues
```

---

## ⚡ **Solution 3: Alternative EAS Configuration**

If you must use Expo SDK 52, try this configuration:

### **Step 1: Update eas.json**
```json
{
  "build": {
    "development": {
      "developmentClient": true,
      "distribution": "internal",
      "android": {
        "buildType": "apk",
        "gradleCommand": ":app:assembleDebug"
      },
      "env": {
        "EXPO_NO_CAPABILITY_SYNC": "1",
        "EXPO_NO_GCM": "1"
      }
    }
  }
}
```

### **Step 2: Simplify app.json plugins**
Remove problematic plugins temporarily:
```json
{
  "plugins": [
    "expo-router",
    [
      "expo-build-properties",
      {
        "android": {
          "compileSdkVersion": 33,
          "targetSdkVersion": 33,
          "minSdkVersion": 23
        }
      }
    ]
  ]
}
```

---

## 🎯 **RECOMMENDED APPROACH**

For your **POS machine deployment**, I recommend:

### **Option A: Expo SDK 51 (Stable)**
```bash
# 1. Downgrade to SDK 51
npx expo install expo@~51.0.0
npx expo install --fix

# 2. Build
eas build --profile production --platform android
```

### **Option B: Local Build (Fastest)**
```bash
# 1. Install Android Studio
# 2. Connect device/emulator
# 3. Build locally
npx expo run:android --variant release
```

---

## 📱 **Current Status**

Your app configuration is now optimized for:
- ✅ **Bluetooth printing functionality**
- ✅ **Thermal receipt format**
- ✅ **Production-ready features**

The only issue is the **build process**, not your code!

---

## 🔍 **Quick Diagnosis**

Run this to check your setup:
```bash
# Check Expo version
npx expo --version

# Check EAS CLI version
eas --version

# Check project health
npx expo doctor

# Check dependencies
npm ls expo
```

---

## 🚀 **Next Steps**

1. **Try Solution 1** (Expo SDK 51) - Most reliable
2. **If that fails, try Solution 2** (Local build) - Fastest
3. **If you need EAS, try Solution 3** (Alternative config)

---

## 💡 **Why This Happens**

- **Expo SDK 52** is newer and has some stability issues
- **Native modules** like `react-native-ble-plx` need stable build environment
- **Gradle plugin conflicts** are common in newer SDK versions

---

## ✅ **Expected Results**

After applying any solution:
- ✅ **Build completes successfully**
- ✅ **APK installs on device**
- ✅ **Bluetooth printing works**
- ✅ **All app features functional**

Your app is **ready for production** - just need to get past the build issue! 🎉

---

## 🆘 **If All Else Fails**

Create a minimal test build:
```bash
# Create new minimal Expo app
npx create-expo-app test-build
cd test-build

# Copy your source files
# Build to verify EAS setup
eas build --profile development --platform android
```

The issue is **definitely** with Expo SDK 52 compatibility, not your code! 🔧
