import React, { useState, useEffect, useContext } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import * as Location from 'expo-location';
import { ThemeContext } from '../contexts/ThemeContext';

const GPSStatusIndicator = () => {
  const [accuracy, setAccuracy] = useState(null);
  const { theme, mode } = useContext(ThemeContext);

  useEffect(() => {
    let isMounted = true;
    
    const checkAccuracy = async () => {
      try {
        const location = await Location.getCurrentPositionAsync({
          accuracy: Location.Accuracy.BestForNavigation
        });
        
        if (isMounted) {
          setAccuracy(location.coords.accuracy);
        }
      } catch (error) {
        if (isMounted) {
          setAccuracy(null);
        }
      }
    };

    const interval = setInterval(checkAccuracy, 10000);
    checkAccuracy(); // Initial check
    
    return () => {
      isMounted = false;
      clearInterval(interval);
    };
  }, []);

  const getStatusInfo = () => {
    if (!accuracy) return { icon: '⚪', text: 'No Signal', color: '#808080' };
    if (accuracy <= 20) return { icon: '🟢', text: 'Strong', color: '#4CAF50' };
    if (accuracy <= 50) return { icon: '🟡', text: 'Moderate', color: '#FFC107' };
    return { icon: '🔴', text: 'Weak', color: '#FF5252' };
  };

  const statusInfo = getStatusInfo();

  return (
    <View style={[
      styles.container,
      {
        backgroundColor: mode === 'dark' ? 'rgba(0, 0, 0, 0.7)' : 'rgba(255, 255, 255, 0.9)',
        borderColor: mode === 'dark' ? theme.borderColor : theme.primary,
      }
    ]}>
      <View style={styles.content}>
        <Text style={[
          styles.label,
          { color: mode === 'dark' ? '#FFFFFF' : '#000000' }
        ]}>
          GPS Signal
        </Text>
        <View style={styles.statusContainer}>
          <Text style={styles.icon}>{statusInfo.icon}</Text>
          <Text style={[
            styles.statusText,
            { color: statusInfo.color }
          ]}>
            {statusInfo.text}
          </Text>
        </View>
        {accuracy && (
          <Text style={[
            styles.accuracyText,
            { color: mode === 'dark' ? '#CCCCCC' : '#666666' }
          ]}>
            ±{Math.round(accuracy)}m
          </Text>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    borderWidth: 1,
    padding: 8,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  content: {
    alignItems: 'center',
  },
  label: {
    fontSize: 12,
    fontWeight: '500',
    marginBottom: 4,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  icon: {
    fontSize: 16,
  },
  statusText: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  accuracyText: {
    fontSize: 10,
    marginTop: 2,
    fontWeight: '500',
  }
});

export default GPSStatusIndicator;

