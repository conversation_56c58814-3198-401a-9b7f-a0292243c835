// BusStopRestAPI.js - Simple REST API client for formatted bus stop data
import axios from 'axios';

// API endpoint for formatted bus data
const API_ENDPOINT = 'https://ujz4d1yll0.execute-api.ap-south-2.amazonaws.com/Development/GetBusData';

/**
 * Fetches already formatted bus stop data from the REST API and logs it
 * @param {string} busNumber - The bus number to fetch data for
 * @returns {Promise<Object>} - The raw formatted data from the API
 */
export async function fetchBusDataFromAPI(busNumber) {
  try {
    console.log(`[BusStopRestAPI] Fetching data for bus ${busNumber} from API`);
    
    // Make API request
    const response = await axios.post(API_ENDPOINT, {
        busNumber: busNumber
      });
    
    // Check if the response is successful
    if (response.data && response.status === 200) {
      console.log(`[BusStopRestAPI] Successfully received data from API for bus ${busNumber}`);
      
      // Parse the response body if it's a string
      let busData = response.data;
      if (busData.body && typeof busData.body === 'string') {
        try {
          busData = JSON.parse(busData.body);
        } catch (e) {
          console.error('[BusStopRestAPI] Error parsing response body:', e);
          return null;
        }
      }
      // Check if the response has stops array
      if (busData.stops && Array.isArray(busData.stops)) {
        console.log(`[BusStopRestAPI] Received ${busData.stops.length} stops for bus ${busNumber}`);
      } else {
        console.log('[BusStopRestAPI] No bus stops found in the API response');
      } 
      // Return the parsed data for use in the app
      return busData;
    } else {
      console.error(`[BusStopRestAPI] API returned error status: ${response.status}`);
      return null;
    }
  } catch (error) {
    console.error(`[BusStopRestAPI] Error fetching bus data:`, error.message);
    return null;
  }
}