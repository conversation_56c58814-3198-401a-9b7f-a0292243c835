import { StyleSheet } from 'react-native';

const createStyles = (theme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: 20,
      paddingTop: 20,
      paddingBottom: 20,
      backgroundColor: theme.background,
    },
    backButton: {
      padding: 8,
      borderRadius: 20,
      backgroundColor: theme.secondaryBackground,
    },
    headerTitle: {
      fontSize: 20,
      fontWeight: 'bold',
      color: theme.text,
      flex: 1,
      textAlign: 'center',
      marginHorizontal: 20,
    },
    // Main content area (flex layout)
    mainContentArea: {
      flex: 1,
      paddingHorizontal: 20,
      paddingTop: 20,
    },
    // Combined card for fare and dropdowns with enhanced shadow
    fareAndDropdownCard: {
      backgroundColor: theme.secondaryBackground,
      borderRadius: 15,
      padding: 20,
      marginBottom: 20,
      shadowColor: theme.shadow,
      shadowOpacity: 0.15,
      shadowOffset: { width: 0, height: 6 },
      shadowRadius: 12,
      elevation: 8,
    },
    fareContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      marginBottom: 20,
    },
    fareLabel: {
      fontSize: 18,
      fontWeight: '600',
      color: theme.text,
      marginRight: 10,
    },
    fareAmount: {
      fontSize: 24,
      fontWeight: 'bold',
      color: '#FF0000',
    },
    dropdownContainer: {
      marginBottom: 15,
    },
    dropdown: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      backgroundColor: theme.background,
      borderRadius: 10,
      paddingHorizontal: 15,
      paddingVertical: 15,
      borderWidth: 1,
      borderColor: theme.borderColor,
    },
    dropdownText: {
      fontSize: 16,
      color: theme.text,
      flex: 1,
    },
    dropdownList: {
      backgroundColor: theme.secondaryBackground,
      borderRadius: 8,
      marginTop: 5,
      maxHeight: 200,
      borderWidth: 1,
      borderColor: theme.borderColor,
      shadowColor: theme.shadow,
      shadowOpacity: 0.1,
      shadowOffset: { width: 0, height: 2 },
      shadowRadius: 5,
      elevation: 3,
    },
    dropdownItem: {
      paddingHorizontal: 15,
      paddingVertical: 12,
      borderBottomWidth: 1,
      borderBottomColor: theme.borderColor,
    },
    dropdownItemText: {
      fontSize: 16,
      color: theme.text,
    },
    // Scrollable quick actions
    quickActionsScrollView: {
      flex: 1,
      marginBottom: 10,
    },
    quickActionsContent: {
      paddingVertical: 10,
    },
    quickActionButton: {
      backgroundColor: '#FFFFFF',
      borderRadius: 50, // Fully rounded
      paddingHorizontal: 20,
      paddingVertical: 12,
      marginBottom: 10,
      alignItems: 'center',
      borderWidth: 1,
      borderColor: theme.borderColor,
      shadowColor: theme.shadow,
      shadowOpacity: 0.05,
      shadowOffset: { width: 0, height: 2 },
      shadowRadius: 4,
      elevation: 2,
    },
    quickActionText: {
      color: theme.text,
      fontSize: 14,
      fontWeight: '500',
    },
    // Bottom fixed container
    bottomButtonsContainer: {
      backgroundColor: theme.background,
      paddingHorizontal: 20,
      paddingTop: 15,
      paddingBottom: 30,
      // borderTopWidth: 1,
      // borderTopColor: theme.borderColor,
    },
    passengerTypeContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: 20,
    },
    passengerTypeButton: {
      flex: 1,
      backgroundColor: theme.secondaryBackground,
      borderRadius: 50, // Fully rounded
      paddingVertical: 15,
      marginHorizontal: 5,
      alignItems: 'center',
      borderWidth: 2,
      borderColor: theme.borderColor,
    },
    passengerTypeButtonActive: {
      backgroundColor: theme.primary,
      borderColor: theme.primary,
    },
    passengerTypeText: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.text,
    },
    passengerTypeTextActive: {
      color: theme.normal_text,
    },
    actionButtonsContainer: {
      marginBottom: 0,
    },
    printButton: {
      backgroundColor: theme.primary,
      borderRadius: 50, // Fully rounded
      paddingVertical: 18,
      alignItems: 'center',
      marginBottom: 15,
      shadowColor: theme.shadow,
      shadowOpacity: 0.2,
      shadowOffset: { width: 0, height: 4 },
      shadowRadius: 8,
      elevation: 5,
    },
    printButtonText: {
      fontSize: 18,
      fontWeight: 'bold',
      color: theme.normal_text,
    },
    bottomButtonsRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    luggageButton: {
      flex: 1,
      backgroundColor: theme.secondaryBackground,
      borderRadius: 50, // Fully rounded
      paddingVertical: 15,
      marginRight: 10,
      alignItems: 'center',
      borderWidth: 1,
      borderColor: theme.borderColor,
    },
    luggageButtonText: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.text,
    },
    cancelButton: {
      flex: 1,
      backgroundColor: '#FF4444',
      borderRadius: 50, // Fully rounded
      paddingVertical: 15,
      marginLeft: 10,
      alignItems: 'center',
    },
    cancelButtonText: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.normal_text,
    },
  });

export default createStyles;
