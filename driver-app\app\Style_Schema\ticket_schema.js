import { StyleSheet } from 'react-native';

const createStyles = (theme, mode) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: 20,
      paddingTop: 20,
      paddingBottom: 20,
      backgroundColor: theme.background,
    },
    backButton: {
      padding: 8,
      borderRadius: 20,
      backgroundColor: theme.secondaryBackground,
    },
    headerTitle: {
      fontSize: 20,
      fontWeight: 'bold',
      color: theme.text,
      flex: 1,
      textAlign: 'center',
      marginHorizontal: 20,
    },
    // Main content area (flex layout)
    mainContentArea: {
      flex: 1,
      paddingHorizontal: 20,
      paddingTop: 10,
      zIndex: 1, // Base z-index for main content
    },
    // Combined card for fare and dropdowns with box shadow drop effect
    fareAndDropdownCard: {
      backgroundColor: theme.secondaryBackground,
      borderRadius: 15,
      padding: 20,
      marginBottom: 20,
      // Box shadow drop effect
      shadowColor: '#000000',
      shadowOpacity: 0.25,
      shadowOffset: { width: 0, height: 8 },
      shadowRadius: 16,
      elevation: 12,
    },
    fareContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      marginBottom: 20,
    },
    fareLabel: {
      fontSize: 18,
      fontWeight: '600',
      color: theme.text,
      marginRight: 10,
    },
    fareAmount: {
      fontSize: 24,
      fontWeight: 'bold',
      color: '#FF0000',
    },
    dropdownContainer: {
      marginBottom: 15,
      zIndex: 100, // Higher than quick actions
      position: 'relative',
      elevation: 100, // Android elevation
    },
    dropdown: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      backgroundColor: mode === 'dark' ? '#2A2A2A' : theme.background, // Dark mode optimization
      borderRadius: 10,
      paddingHorizontal: 15,
      paddingVertical: 15,
      borderWidth: 1,
      borderColor: theme.borderColor,
    },
    dropdownText: {
      fontSize: 16,
      color: theme.text,
      flex: 1,
    },
    // Dropdown overlay that covers entire screen
    dropdownOverlay: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      zIndex: 999999, // Maximum possible z-index
      elevation: 999, // Maximum Android elevation
      backgroundColor: 'rgba(0, 0, 0, 0.1)', // Very light overlay to block background
      pointerEvents: 'box-none', // Allow touches to pass through to dropdown content
    },
    // Floating dropdown styles with maximum z-index
    floatingDropdownContainer: {
      position: 'absolute',
      top: '100%',
      left: 0,
      right: 0,
      zIndex: 99999, // Maximum z-index to appear above everything
      marginTop: 5,
      elevation: 999, // Android elevation
    },
    floatingDropdown: {
      backgroundColor: theme.secondaryBackground,
      borderRadius: 15,
      maxHeight: 200,
      shadowColor: '#000000',
      shadowOpacity: 0.3,
      shadowOffset: { width: 0, height: 8 },
      shadowRadius: 16,
      elevation: 999, // Maximum Android elevation
      flexDirection: 'row',
      zIndex: 99999,
      // Ensure completely opaque background
      opacity: 1.0,
    },
    // Positioning for From dropdown
    fromDropdownPosition: {
      position: 'absolute',
      top: 200,
      left: 20,
      right: 20,
      marginHorizontal: 0, // Ensure no extra margins
    },
    // Positioning for To dropdown
    toDropdownPosition: {
      position: 'absolute',
      top: 260,
      left: 20,
      right: 20,
      marginHorizontal: 0, // Ensure no extra margins
    },
    dropdownScrollView: {
      flex: 1,
      borderRadius: 15,
      backgroundColor: theme.secondaryBackground, // Ensure opaque background
      opacity: 1.0, // Completely opaque
    },
    scrollbarTrack: {
      width: 6,
      height: 160, // Fixed height for calculations
      backgroundColor: '#7B7B7B', // Grey track
      borderRadius: 3,
      marginVertical: 8,
      marginRight: 8,
      justifyContent: 'flex-start',
      alignItems: 'center',
      position: 'relative',
    },
    scrollbarThumb: {
      width: 4,
      height: 60, // Fixed height for smooth movement
      borderRadius: 2,
      position: 'absolute',
      top: 0,
      left: 1, // Center in track
    },
    dropdownItem: {
      paddingHorizontal: 15,
      paddingVertical: 12,
      borderBottomWidth: 1,
      borderBottomColor: theme.borderColor,
      backgroundColor: theme.secondaryBackground, // Ensure opaque background
      opacity: 1.0, // Completely opaque
    },
    dropdownItemText: {
      fontSize: 16,
      color: theme.text,
      opacity: 1.0, // Ensure text is completely opaque
    },
    // Scrollable quick actions with blur effect
    quickActionsScrollView: {
      flex: 1,
      marginBottom: 10,
      position: 'relative',
      zIndex: 1, // Much lower than dropdowns
      elevation: 1, // Low Android elevation
    },
    quickActionsContent: {
      paddingVertical: 10,
      paddingBottom: 40, // Extra padding for blur effect
    },
    quickActionsWrapper: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      justifyContent: 'space-between',
    },
    quickActionButtonContainer: {
      width: '48%', // Two buttons per row with spacing
      marginBottom: 10,
    },
    quickActionGradientBorder: {
      borderRadius: 50,
      padding: 2, // Border thickness
    },
    quickActionButton: {
      backgroundColor: mode === 'dark' ? '#2A2A2A' : '#FFFFFF', // Dark mode optimization
      borderRadius: 48, // Slightly smaller to show gradient border
      paddingHorizontal: 18,
      paddingVertical: 10,
      alignItems: 'center',
      shadowColor: theme.shadow,
      shadowOpacity: 0.05,
      shadowOffset: { width: 0, height: 2 },
      shadowRadius: 4,
      elevation: 2,
    },
    blurEffect: {
      position: 'absolute',
      bottom: 0,
      left: 0,
      right: 0,
      height: 40, // Increased height for better disappearing effect
      pointerEvents: 'none', // Allow touches to pass through
    },
    separatorContainer: {
      marginTop: 10,
      marginBottom: 5,
      paddingHorizontal: 20,
    },
    separatorLineGradient: {
      height: 1,
      opacity: 0.5,
    },
    quickActionText: {
      color: mode === 'dark' ? '#FFFFFF' : theme.text, // Better contrast in dark mode
      fontSize: 14,
      fontWeight: '500',
    },
    // Bottom fixed container
    bottomButtonsContainer: {
      backgroundColor: theme.background,
      paddingHorizontal: 20,
      paddingTop: 15,
      paddingBottom: 30,
      // borderTopWidth: 1,
      // borderTopColor: theme.borderColor,
    },
    passengerTypeContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: 20,
    },
    passengerTypeButton: {
      flex: 1,
      backgroundColor: theme.secondaryBackground,
      borderRadius: 50, // Fully rounded
      paddingVertical: 0, // Remove padding to let inner handle it
      marginHorizontal: 5,
      alignItems: 'center',
      justifyContent: 'center',
      borderWidth: 2,
      borderColor: '#7B7B7B', // Updated to #7B7B7B for unselected state
      minHeight: 50, // Consistent height
    },
    passengerTypeButtonInner: {
      flex: 1,
      width: '100%',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 15,
      borderRadius: 48, // Slightly smaller than container for gradient visibility
      minHeight: 50, // Ensure consistent height for proper centering
    },
    passengerTypeText: {
      fontSize: 16,
      fontWeight: '600',
      color: '#7B7B7B', // Grey for unselected state
      textAlign: 'center',
    },
    passengerTypeTextActive: {
      fontSize: 16,
      fontWeight: '700', // Bolder for better visibility
      color: '#FFFFFF', // White text for gradient background
      textAlign: 'center',
      textShadowColor: 'rgba(0, 0, 0, 0.3)', // Add text shadow for better visibility
      textShadowOffset: { width: 0, height: 1 },
      textShadowRadius: 2,
    },
    actionButtonsContainer: {
      marginBottom: 0,
    },
    printButton: {
      borderRadius: 50, // Fully rounded
      marginBottom: 15,
      shadowColor: theme.shadow,
      shadowOpacity: 0.2,
      shadowOffset: { width: 0, height: 4 },
      shadowRadius: 8,
      elevation: 5,
    },
    printButtonInner: {
      paddingVertical: 18,
      alignItems: 'center',
      justifyContent: 'center',
      width: '100%',
    },
    printButtonText: {
      fontSize: 18,
      fontWeight: 'bold',
      color: '#FFFFFF', // White text for gradient background
    },
    bottomButtonsRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    luggageButton: {
      flex: 1,
      backgroundColor: theme.secondaryBackground,
      borderRadius: 50, // Fully rounded
      paddingVertical: 15,
      marginRight: 10,
      alignItems: 'center',
      borderWidth: 1,
      borderColor: '#7B7B7B', // Grey border
    },
    luggageButtonText: {
      fontSize: 16,
      fontWeight: '600',
      color: '#7B7B7B', // Grey text
    },
    cancelButton: {
      flex: 1,
      backgroundColor: '#FF4444',
      borderRadius: 50, // Fully rounded
      paddingVertical: 15,
      marginLeft: 10,
      alignItems: 'center',
    },
    cancelButtonText: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.normal_text,
    },
  });

export default createStyles;
