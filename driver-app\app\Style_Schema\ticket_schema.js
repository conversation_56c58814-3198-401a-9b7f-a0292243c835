import { StyleSheet } from 'react-native';

const createStyles = (theme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: 20,
      paddingTop: 20,
      paddingBottom: 20,
      backgroundColor: theme.background,
    },
    backButton: {
      padding: 8,
      borderRadius: 20,
      backgroundColor: theme.secondaryBackground,
    },
    headerTitle: {
      fontSize: 20,
      fontWeight: 'bold',
      color: theme.text,
      flex: 1,
      textAlign: 'center',
      marginHorizontal: 20,
    },
    // Main content area (flex layout)
    mainContentArea: {
      flex: 1,
      paddingHorizontal: 20,
      paddingTop: 20,
    },
    // Combined card for fare and dropdowns with box shadow drop effect
    fareAndDropdownCard: {
      backgroundColor: theme.secondaryBackground,
      borderRadius: 15,
      padding: 20,
      marginBottom: 20,
      // Box shadow drop effect
      shadowColor: '#000000',
      shadowOpacity: 0.25,
      shadowOffset: { width: 0, height: 8 },
      shadowRadius: 16,
      elevation: 12,
    },
    fareContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      marginBottom: 20,
    },
    fareLabel: {
      fontSize: 18,
      fontWeight: '600',
      color: theme.text,
      marginRight: 10,
    },
    fareAmount: {
      fontSize: 24,
      fontWeight: 'bold',
      color: '#FF0000',
    },
    dropdownContainer: {
      marginBottom: 15,
    },
    dropdown: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      backgroundColor: theme.background,
      borderRadius: 10,
      paddingHorizontal: 15,
      paddingVertical: 15,
      borderWidth: 1,
      borderColor: theme.borderColor,
    },
    dropdownText: {
      fontSize: 16,
      color: theme.text,
      flex: 1,
    },
    dropdownList: {
      backgroundColor: theme.secondaryBackground,
      borderRadius: 8,
      marginTop: 5,
      maxHeight: 200,
      borderWidth: 1,
      borderColor: theme.borderColor,
      shadowColor: theme.shadow,
      shadowOpacity: 0.1,
      shadowOffset: { width: 0, height: 2 },
      shadowRadius: 5,
      elevation: 3,
    },
    dropdownItem: {
      paddingHorizontal: 15,
      paddingVertical: 12,
      borderBottomWidth: 1,
      borderBottomColor: theme.borderColor,
    },
    dropdownItemText: {
      fontSize: 16,
      color: theme.text,
    },
    // Scrollable quick actions with blur effect
    quickActionsScrollView: {
      flex: 1,
      marginBottom: 10,
      position: 'relative',
    },
    quickActionsContent: {
      paddingVertical: 10,
      paddingBottom: 40, // Extra padding for blur effect
    },
    quickActionsWrapper: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      justifyContent: 'space-between',
    },
    quickActionButtonContainer: {
      width: '48%', // Two buttons per row with spacing
      marginBottom: 10,
    },
    quickActionGradientBorder: {
      borderRadius: 50,
      padding: 2, // Border thickness
    },
    quickActionButton: {
      backgroundColor: '#FFFFFF',
      borderRadius: 48, // Slightly smaller to show gradient border
      paddingHorizontal: 18,
      paddingVertical: 10,
      alignItems: 'center',
      shadowColor: theme.shadow,
      shadowOpacity: 0.05,
      shadowOffset: { width: 0, height: 2 },
      shadowRadius: 4,
      elevation: 2,
    },
    blurEffect: {
      position: 'absolute',
      bottom: 0,
      left: 0,
      right: 0,
      height: 30,
      backgroundColor: 'transparent',
      background: `linear-gradient(to bottom, transparent, ${theme.background})`,
      opacity: 0.8,
    },
    quickActionText: {
      color: theme.text,
      fontSize: 14,
      fontWeight: '500',
    },
    // Bottom fixed container
    bottomButtonsContainer: {
      backgroundColor: theme.background,
      paddingHorizontal: 20,
      paddingTop: 15,
      paddingBottom: 30,
      // borderTopWidth: 1,
      // borderTopColor: theme.borderColor,
    },
    passengerTypeContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: 20,
    },
    passengerTypeButton: {
      flex: 1,
      backgroundColor: theme.secondaryBackground,
      borderRadius: 50, // Fully rounded
      paddingVertical: 15,
      marginHorizontal: 5,
      alignItems: 'center',
      borderWidth: 2,
      borderColor: '#7B7B7B', // Updated to #7B7B7B for unselected state
    },
    passengerTypeButtonInner: {
      flex: 1,
      width: '100%',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 15,
    },
    passengerTypeText: {
      fontSize: 16,
      fontWeight: '600',
      color: '#7B7B7B', // Updated to #7B7B7B for unselected state
    },
    passengerTypeTextActive: {
      fontSize: 16,
      fontWeight: '600',
      color: '#FFFFFF', // White text for gradient background
    },
    actionButtonsContainer: {
      marginBottom: 0,
    },
    printButton: {
      borderRadius: 50, // Fully rounded
      marginBottom: 15,
      shadowColor: theme.shadow,
      shadowOpacity: 0.2,
      shadowOffset: { width: 0, height: 4 },
      shadowRadius: 8,
      elevation: 5,
    },
    printButtonInner: {
      paddingVertical: 18,
      alignItems: 'center',
      justifyContent: 'center',
      width: '100%',
    },
    printButtonText: {
      fontSize: 18,
      fontWeight: 'bold',
      color: '#FFFFFF', // White text for gradient background
    },
    bottomButtonsRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    luggageButton: {
      flex: 1,
      backgroundColor: theme.secondaryBackground,
      borderRadius: 50, // Fully rounded
      paddingVertical: 15,
      marginRight: 10,
      alignItems: 'center',
      borderWidth: 1,
      borderColor: theme.borderColor,
    },
    luggageButtonText: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.text,
    },
    cancelButton: {
      flex: 1,
      backgroundColor: '#FF4444',
      borderRadius: 50, // Fully rounded
      paddingVertical: 15,
      marginLeft: 10,
      alignItems: 'center',
    },
    cancelButtonText: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.normal_text,
    },
  });

export default createStyles;
