import React from 'react';
import { View, Text, ActivityIndicator, StyleSheet } from 'react-native';
import { useTranslation } from 'react-i18next';

const LoadingIndicator = ({ 
  message, 
  color = '#FFFFFF', 
  size = 'large', 
  textStyle = {},
  containerStyle = {} 
}) => {
  const { t } = useTranslation();
  
  // Use provided message or default translation
  const displayMessage = message || t('common.loading');
  
  return (
    <View style={[styles.loadingContainer, containerStyle]}>
      <Text style={[styles.loadingText, { color }, textStyle]}>
        {displayMessage}
      </Text>
      <ActivityIndicator size={size} color={color} />
    </View>
  );
};

const styles = StyleSheet.create({
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 10,
  },
  loadingText: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
  },
});

export default LoadingIndicator;