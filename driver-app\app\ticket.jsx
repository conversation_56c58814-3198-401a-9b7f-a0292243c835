import React, { useState, useContext, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Alert
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { ThemeContext } from '../contexts/ThemeContext';
import { useTranslation } from 'react-i18next';
import FontAwesome6 from '@expo/vector-icons/FontAwesome6';
import ColorModeToggle from '../components/ColorModeToggle';
import { LinearGradient } from 'expo-linear-gradient';
import createStyles from './Style_Schema/ticket_schema';
import BluetoothPrinterService from '../services/BluetoothPrinterService';
import PDFPrintService from '../services/PDFPrintService';
import PrinterSelectionModal from '../components/PrinterSelectionModal';

export default function TicketScreen() {
  const { theme, mode } = useContext(ThemeContext);
  const styles = createStyles(theme, mode);
  const { t } = useTranslation();
  const navigation = useNavigation();
  const route = useRoute();
  const busNumber = route.params?.busNumber;

  // State for ticket form
  const [fromCity, setFromCity] = useState('');
  const [toCity, setToCity] = useState('');
  const [passengerType, setPassengerType] = useState('adult'); // 'child', 'adult', 'buspass'
  const [fareAmount, setFareAmount] = useState('0.00');
  const [showFromDropdown, setShowFromDropdown] = useState(false);
  const [showToDropdown, setShowToDropdown] = useState(false);

  // Scrollbar state for dropdowns
  const [fromScrollPosition, setFromScrollPosition] = useState(0);
  const [toScrollPosition, setToScrollPosition] = useState(0);

  // Bluetooth printing state
  const [showPrinterModal, setShowPrinterModal] = useState(false);
  const [connectedPrinter, setConnectedPrinter] = useState(null);
  const [isPrinting, setIsPrinting] = useState(false);

  // Available cities
  const cities = ['Mayiladuthurai', 'Karaikal', 'Ambagathur', 'Nattarmbal', 'Sangar', 'Thiruvarur', 'Nagapattinam'];

  // Quick action stops - expanded for testing scroll and blur effect
  const quickStops = [
    'Ambagathur', 'Nattarmbal', 'Sangar', 'Thiruvarur',
    'Mayiladuthurai', 'Karaikal', 'Nagapattinam', 'Vedaranyam',
    'Tharangambadi', 'Poompuhar', 'Sirkazhi', 'Chidambaram',
    'Cuddalore', 'Villupuram', 'Tindivanam', 'Gingee',
    'Vandavasi', 'Kanchipuram', 'Tambaram', 'Chennai'
  ];

  // Initialize Bluetooth service and check for connected printer
  useEffect(() => {
    const initializeBluetooth = async () => {
      await BluetoothPrinterService.initialize();
      const deviceInfo = BluetoothPrinterService.getConnectedDeviceInfo();
      setConnectedPrinter(deviceInfo);
    };

    initializeBluetooth();

    // Cleanup on unmount
    return () => {
      BluetoothPrinterService.destroy();
    };
  }, []);

  const handleBackPress = () => {
    navigation.goBack();
  };

  const handleFromCitySelect = (city) => {
    setFromCity(city);
    setShowFromDropdown(false);
    setShowToDropdown(false); // Close other dropdown
    calculateFare(city, toCity, passengerType);
  };

  const handleToCitySelect = (city) => {
    setToCity(city);
    setShowToDropdown(false);
    setShowFromDropdown(false); // Close other dropdown
    calculateFare(fromCity, city, passengerType);
  };

  const handleQuickStopPress = (stopName) => {
    setToCity(stopName);
    setShowToDropdown(false);
    setShowFromDropdown(false); // Close both dropdowns
    calculateFare(fromCity, stopName, passengerType);
  };

  // Toggle From dropdown and close To dropdown
  const toggleFromDropdown = () => {
    setShowFromDropdown(!showFromDropdown);
    setShowToDropdown(false); // Close To dropdown
  };

  // Toggle To dropdown and close From dropdown
  const toggleToDropdown = () => {
    setShowToDropdown(!showToDropdown);
    setShowFromDropdown(false); // Close From dropdown
  };

  // Close all dropdowns when clicking outside
  const closeAllDropdowns = () => {
    setShowFromDropdown(false);
    setShowToDropdown(false);
  };

  const calculateFare = (from, to, type) => {
    // Basic fare calculation logic - you can enhance this
    let baseFare = 10.00;

    if (from && to && from !== to) {
      // Calculate distance-based fare (placeholder logic)
      baseFare = 15.00;
    }

    switch (type) {
      case 'child':
        setFareAmount((baseFare * 0.5).toFixed(2));
        break;
      case 'adult':
        setFareAmount(baseFare.toFixed(2));
        break;
      case 'buspass':
        setFareAmount('0.00');
        break;
      default:
        setFareAmount(baseFare.toFixed(2));
    }
  };

  const handlePassengerTypePress = (type) => {
    setPassengerType(type);
    calculateFare(fromCity, toCity, type);
  };

  const handlePrintPress = async () => {
    // Validate ticket data
    if (!fromCity || !toCity) {
      Alert.alert(
        'Incomplete Information',
        'Please select both departure and destination cities before printing.',
        [{ text: 'OK' }]
      );
      return;
    }

    // Check if printer is connected, if not offer alternatives
    if (!BluetoothPrinterService.isConnected()) {
      Alert.alert(
        'Choose Printing Method',
        'Select how you would like to print your ticket:',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Bluetooth Printer', onPress: () => setShowPrinterModal(true) },
          { text: 'PDF/Share', onPress: () => handlePDFPrint() }
        ]
      );
      return;
    }

    setIsPrinting(true);
    try {
      // Generate ticket number
      const ticketNumber = `${Date.now().toString().slice(-6)}`;
      const currentDate = new Date();

      const ticketData = {
        ticketNumber,
        date: currentDate.toLocaleDateString(),
        time: currentDate.toLocaleTimeString(),
        fromCity,
        toCity,
        passengerType: passengerType === 'buspass' ? 'Bus Pass' :
                     passengerType === 'child' ? 'Child' : 'Adult',
        fareAmount,
        busNumber: busNumber || 'N/A'
      };

      await BluetoothPrinterService.printTicket(ticketData);

      Alert.alert(
        'Print Successful',
        'Ticket has been printed successfully!',
        [{ text: 'OK' }]
      );
    } catch (error) {
      console.error('Print error:', error);
      Alert.alert(
        'Print Failed',
        `Failed to print ticket: ${error.message}`,
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Try Again', onPress: handlePrintPress },
          { text: 'Select Printer', onPress: () => setShowPrinterModal(true) }
        ]
      );
    } finally {
      setIsPrinting(false);
    }
  };

  const handlePrinterSelected = (printer) => {
    setConnectedPrinter({ id: printer.id, name: printer.name });
  };

  const handlePDFPrint = async () => {
    // Validate ticket data
    if (!fromCity || !toCity) {
      Alert.alert(
        'Incomplete Information',
        'Please select both departure and destination cities before printing.',
        [{ text: 'OK' }]
      );
      return;
    }

    setIsPrinting(true);
    try {
      // Generate ticket number
      const ticketNumber = `${Date.now().toString().slice(-6)}`;
      const currentDate = new Date();

      const ticketData = {
        ticketNumber,
        date: currentDate.toLocaleDateString(),
        time: currentDate.toLocaleTimeString(),
        fromCity,
        toCity,
        passengerType: passengerType === 'buspass' ? 'Bus Pass' :
                     passengerType === 'child' ? 'Child' : 'Adult',
        fareAmount,
        busNumber: busNumber || 'N/A'
      };

      await PDFPrintService.printTicket(ticketData);

      Alert.alert(
        'Ticket Generated',
        'Ticket has been generated successfully! You can now print or save it.',
        [{ text: 'OK' }]
      );
    } catch (error) {
      console.error('PDF Print error:', error);
      Alert.alert(
        'Print Failed',
        `Failed to generate ticket: ${error.message}`,
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Try Again', onPress: handlePDFPrint }
        ]
      );
    } finally {
      setIsPrinting(false);
    }
  };

  const handleLuggagePress = () => {
    Alert.alert(
      t('ticket.alerts.luggageTitle'),
      t('ticket.alerts.luggageMessage'),
      [
        { text: t('common.ok'), style: 'default' }
      ]
    );
  };

  const handleCancelPress = () => {
    Alert.alert(
      t('ticket.alerts.cancelTitle'),
      t('ticket.alerts.cancelMessage'),
      [
        { text: t('common.cancel'), style: 'cancel' },
        { text: t('common.confirm'), onPress: () => navigation.goBack() }
      ]
    );
  };

  // Handle scroll events for dropdown scrollbars with optimized performance
  const handleFromDropdownScroll = (event) => {
    const { contentOffset, contentSize, layoutMeasurement } = event.nativeEvent;
    if (contentSize.height > layoutMeasurement.height) {
      const maxScroll = contentSize.height - layoutMeasurement.height;
      const scrollPercentage = Math.max(0, Math.min(1, contentOffset.y / maxScroll));
      setFromScrollPosition(scrollPercentage);
    } else {
      setFromScrollPosition(0);
    }
  };

  const handleToDropdownScroll = (event) => {
    const { contentOffset, contentSize, layoutMeasurement } = event.nativeEvent;
    if (contentSize.height > layoutMeasurement.height) {
      const maxScroll = contentSize.height - layoutMeasurement.height;
      const scrollPercentage = Math.max(0, Math.min(1, contentOffset.y / maxScroll));
      setToScrollPosition(scrollPercentage);
    } else {
      setToScrollPosition(0);
    }
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={handleBackPress}>
          <FontAwesome6 name="chevron-left" size={24} color={theme.textColor} />
        </TouchableOpacity>

        <Text style={styles.headerTitle}>{t('ticket.title')}</Text>

        <ColorModeToggle />
      </View>

      {/* Main Content Area */}
      <View style={styles.mainContentArea}>
        {/* Combined Card for Fare Amount and Dropdowns */}
        <View style={styles.fareAndDropdownCard}>
          {/* Fare Amount */}
          <View style={styles.fareContainer}>
            <Text style={styles.fareLabel}>{t('ticket.fareAmount')} :</Text>
            <Text style={styles.fareAmount}>{fareAmount} RS</Text>
          </View>

          {/* From Dropdown */}
          <View style={styles.dropdownContainer}>
            <TouchableOpacity
              style={styles.dropdown}
              onPress={toggleFromDropdown}
            >
              <Text style={styles.dropdownText}>
                {fromCity || t('ticket.fromPlaceholder')}
              </Text>
              <FontAwesome6
                name={showFromDropdown ? "chevron-up" : "chevron-down"}
                size={16}
                color={theme.text}
              />
            </TouchableOpacity>

          </View>

          {/* To Dropdown */}
          <View style={styles.dropdownContainer}>
            <TouchableOpacity
              style={styles.dropdown}
              onPress={toggleToDropdown}
            >
              <Text style={styles.dropdownText}>
                {toCity || t('ticket.toPlaceholder')}
              </Text>
              <FontAwesome6
                name={showToDropdown ? "chevron-up" : "chevron-down"}
                size={16}
                color={theme.text}
              />
            </TouchableOpacity>

          </View>
        </View>

        {/* Scrollable Quick Action Buttons with Flex Wrap */}
        <ScrollView
          style={styles.quickActionsScrollView}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.quickActionsContent}
        >
          <View style={styles.quickActionsWrapper}>
            {quickStops.map((stop, index) => (
              <View key={`${stop}-${index}`} style={styles.quickActionButtonContainer}>
                <LinearGradient
                  colors={['#6777E8', '#95B2EC']}
                  style={styles.quickActionGradientBorder}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                >
                  <TouchableOpacity
                    style={styles.quickActionButton}
                    onPress={() => handleQuickStopPress(stop)}
                  >
                    <Text style={styles.quickActionText}>{stop}</Text>
                  </TouchableOpacity>
                </LinearGradient>
              </View>
            ))}
          </View>
          {/* Enhanced Blur effect at bottom for disappearing effect */}
          <LinearGradient
            colors={[
              'transparent',
              mode === 'dark' ? 'rgba(0, 4, 15, 0.3)' : 'rgba(255, 255, 255, 0.3)',
              mode === 'dark' ? 'rgba(0, 4, 15, 0.7)' : 'rgba(255, 255, 255, 0.7)',
              mode === 'dark' ? '#00040F' : '#FFFFFF'
            ]}
            style={styles.blurEffect}
            start={{ x: 0, y: 0 }}
            end={{ x: 0, y: 1 }}
          />
        </ScrollView>

        {/* Separator Line with Blurred Ends */}
        <View style={styles.separatorContainer}>
          <LinearGradient
            colors={['transparent', theme.borderColor, 'transparent']}
            style={styles.separatorLineGradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
          />
        </View>
      </View>

      {/* Bottom Fixed Buttons */}
      <View style={styles.bottomButtonsContainer}>
        {/* Passenger Type Buttons */}
        <View style={styles.passengerTypeContainer}>
          {/* Child Button */}
          {passengerType === 'child' ? (
            <LinearGradient
              colors={['#6777E8', '#95B2EC']}
              style={[styles.passengerTypeButton, { flex: 1, marginHorizontal: 5 }]}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            >
              <TouchableOpacity
                style={styles.passengerTypeButtonInner}
                onPress={() => handlePassengerTypePress('child')}
              >
                <Text style={styles.passengerTypeTextActive}>
                  {t('ticket.passengerTypes.child')}
                </Text>
              </TouchableOpacity>
            </LinearGradient>
          ) : (
            <TouchableOpacity
              style={styles.passengerTypeButton}
              onPress={() => handlePassengerTypePress('child')}
            >
              <Text style={styles.passengerTypeText}>
                {t('ticket.passengerTypes.child')}
              </Text>
            </TouchableOpacity>
          )}

          {/* Adult Button */}
          {passengerType === 'adult' ? (
            <LinearGradient
              colors={['#6777E8', '#95B2EC']}
              style={[styles.passengerTypeButton, { flex: 1, marginHorizontal: 5 }]}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            >
              <TouchableOpacity
                style={styles.passengerTypeButtonInner}
                onPress={() => handlePassengerTypePress('adult')}
              >
                <Text style={styles.passengerTypeTextActive}>
                  {t('ticket.passengerTypes.adult')}
                </Text>
              </TouchableOpacity>
            </LinearGradient>
          ) : (
            <TouchableOpacity
              style={styles.passengerTypeButton}
              onPress={() => handlePassengerTypePress('adult')}
            >
              <Text style={styles.passengerTypeText}>
                {t('ticket.passengerTypes.adult')}
              </Text>
            </TouchableOpacity>
          )}

          {/* Bus Pass Button */}
          {passengerType === 'buspass' ? (
            <LinearGradient
              colors={['#6777E8', '#95B2EC']}
              style={[styles.passengerTypeButton, { flex: 1, marginHorizontal: 5 }]}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            >
              <TouchableOpacity
                style={styles.passengerTypeButtonInner}
                onPress={() => handlePassengerTypePress('buspass')}
              >
                <Text style={styles.passengerTypeTextActive}>
                  {t('ticket.passengerTypes.busPass')}
                </Text>
              </TouchableOpacity>
            </LinearGradient>
          ) : (
            <TouchableOpacity
              style={styles.passengerTypeButton}
              onPress={() => handlePassengerTypePress('buspass')}
            >
              <Text style={styles.passengerTypeText}>
                {t('ticket.passengerTypes.busPass')}
              </Text>
            </TouchableOpacity>
          )}
        </View>

        {/* Action Buttons */}
        <View style={styles.actionButtonsContainer}>
          {/* Printer Status and Selection */}
          {connectedPrinter ? (
            <TouchableOpacity
              style={[styles.printerStatusButton, { backgroundColor: theme.success + '20', borderColor: theme.success }]}
              onPress={() => setShowPrinterModal(true)}
            >
              <FontAwesome6 name="print" size={16} color={theme.success} />
              <Text style={[styles.printerStatusText, { color: theme.success }]}>
                {connectedPrinter.name}
              </Text>
              <FontAwesome6 name="chevron-right" size={12} color={theme.success} />
            </TouchableOpacity>
          ) : (
            <TouchableOpacity
              style={[styles.printerStatusButton, { backgroundColor: theme.warning + '20', borderColor: theme.warning }]}
              onPress={() => setShowPrinterModal(true)}
            >
              <FontAwesome6 name="print" size={16} color={theme.warning} />
              <Text style={[styles.printerStatusText, { color: theme.warning }]}>
                No Printer Connected
              </Text>
              <FontAwesome6 name="chevron-right" size={12} color={theme.warning} />
            </TouchableOpacity>
          )}

          <LinearGradient
            colors={isPrinting ? ['#95A5A6', '#BDC3C7'] : ['#6777E8', '#95B2EC']}
            style={[styles.printButton, isPrinting && { opacity: 0.7 }]}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          >
            <TouchableOpacity
              style={styles.printButtonInner}
              onPress={handlePrintPress}
              disabled={isPrinting}
            >
              {isPrinting ? (
                <>
                  <FontAwesome6 name="spinner" size={16} color="white" style={{ marginRight: 8 }} />
                  <Text style={styles.printButtonText}>Printing...</Text>
                </>
              ) : (
                <Text style={styles.printButtonText}>{t('ticket.buttons.print')}</Text>
              )}
            </TouchableOpacity>
          </LinearGradient>

          <View style={styles.bottomButtonsRow}>
            <TouchableOpacity style={styles.luggageButton} onPress={handleLuggagePress}>
              <Text style={styles.luggageButtonText}>{t('ticket.buttons.luggage')}</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.cancelButton} onPress={handleCancelPress}>
              <Text style={styles.cancelButtonText}>{t('ticket.buttons.cancel')}</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>

      {/* Dropdown Overlays - Positioned absolutely to appear above everything */}
      {showFromDropdown && (
        <TouchableOpacity
          style={styles.dropdownOverlay}
          activeOpacity={1}
          onPress={closeAllDropdowns}
        >
          <TouchableOpacity
            style={[styles.floatingDropdown, styles.fromDropdownPosition]}
            activeOpacity={1}
            onPress={() => {}} // Prevent event bubbling
          >
            <ScrollView
              style={styles.dropdownScrollView}
              showsVerticalScrollIndicator={false}
              nestedScrollEnabled={true}
              onScroll={handleFromDropdownScroll}
              scrollEventThrottle={8}
            >
              {cities.map((city, index) => (
                <TouchableOpacity
                  key={index}
                  style={styles.dropdownItem}
                  onPress={() => handleFromCitySelect(city)}
                >
                  <Text style={styles.dropdownItemText}>{city}</Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
            {/* Custom Scrollbar with Grey Track and Gradient Thumb */}
            <View style={styles.scrollbarTrack}>
              <LinearGradient
                colors={['#6777E8', '#95B2EC']}
                style={[
                  styles.scrollbarThumb,
                  {
                    transform: [{
                      translateY: fromScrollPosition * 204 // Available movement space (284 - 80)
                    }]
                  }
                ]}
                start={{ x: 0, y: 0 }}
                end={{ x: 0, y: 1 }}
              />
            </View>
          </TouchableOpacity>
        </TouchableOpacity>
      )}

      {showToDropdown && (
        <TouchableOpacity
          style={styles.dropdownOverlay}
          activeOpacity={1}
          onPress={closeAllDropdowns}
        >
          <TouchableOpacity
            style={[styles.floatingDropdown, styles.toDropdownPosition]}
            activeOpacity={1}
            onPress={() => {}} // Prevent event bubbling
          >
            <ScrollView
              style={styles.dropdownScrollView}
              showsVerticalScrollIndicator={false}
              nestedScrollEnabled={true}
              onScroll={handleToDropdownScroll}
              scrollEventThrottle={8}
            >
              {cities.map((city, index) => (
                <TouchableOpacity
                  key={index}
                  style={styles.dropdownItem}
                  onPress={() => handleToCitySelect(city)}
                >
                  <Text style={styles.dropdownItemText}>{city}</Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
            {/* Custom Scrollbar with Grey Track and Gradient Thumb */}
            <View style={styles.scrollbarTrack}>
              <LinearGradient
                colors={['#6777E8', '#95B2EC']}
                style={[
                  styles.scrollbarThumb,
                  {
                    transform: [{
                      translateY: toScrollPosition * 204 // Available movement space (284 - 80)
                    }]
                  }
                ]}
                start={{ x: 0, y: 0 }}
                end={{ x: 0, y: 1 }}
              />
            </View>
          </TouchableOpacity>
        </TouchableOpacity>
      )}

      {/* Printer Selection Modal */}
      <PrinterSelectionModal
        visible={showPrinterModal}
        onClose={() => setShowPrinterModal(false)}
        onPrinterSelected={handlePrinterSelected}
      />
    </View>
  );
}
