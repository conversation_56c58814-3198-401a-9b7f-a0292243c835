import React, { useState, useContext } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Alert
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { ThemeContext } from '../contexts/ThemeContext';
import { useTranslation } from 'react-i18next';
import FontAwesome6 from '@expo/vector-icons/FontAwesome6';
import ColorModeToggle from '../components/ColorModeToggle';
import { LinearGradient } from 'expo-linear-gradient';
import createStyles from './Style_Schema/ticket_schema';

export default function TicketScreen() {
  const { theme, mode } = useContext(ThemeContext);
  const styles = createStyles(theme, mode);
  const { t } = useTranslation();
  const navigation = useNavigation();
  const route = useRoute();
  const busNumber = route.params?.busNumber;

  // State for ticket form
  const [fromCity, setFromCity] = useState('');
  const [toCity, setToCity] = useState('');
  const [passengerType, setPassengerType] = useState('adult'); // 'child', 'adult', 'buspass'
  const [fareAmount, setFareAmount] = useState('0.00');
  const [showFromDropdown, setShowFromDropdown] = useState(false);
  const [showToDropdown, setShowToDropdown] = useState(false);

  // Scrollbar state for dropdowns
  const [fromScrollPosition, setFromScrollPosition] = useState(0);
  const [toScrollPosition, setToScrollPosition] = useState(0);

  // Available cities
  const cities = ['Mayiladuthurai', 'Karaikal', 'Ambagathur', 'Nattarmbal', 'Sangar', 'Thiruvarur', 'Nagapattinam'];

  // Quick action stops - expanded for testing scroll and blur effect
  const quickStops = [
    'Ambagathur', 'Nattarmbal', 'Sangar', 'Thiruvarur',
    'Mayiladuthurai', 'Karaikal', 'Nagapattinam', 'Vedaranyam',
    'Tharangambadi', 'Poompuhar', 'Sirkazhi', 'Chidambaram',
    'Cuddalore', 'Villupuram', 'Tindivanam', 'Gingee',
    'Vandavasi', 'Kanchipuram', 'Tambaram', 'Chennai'
  ];

  const handleBackPress = () => {
    navigation.goBack();
  };

  const handleFromCitySelect = (city) => {
    setFromCity(city);
    setShowFromDropdown(false);
    calculateFare(city, toCity, passengerType);
  };

  const handleToCitySelect = (city) => {
    setToCity(city);
    setShowToDropdown(false);
    calculateFare(fromCity, city, passengerType);
  };

  const handleQuickStopPress = (stopName) => {
    setToCity(stopName);
    setShowToDropdown(false);
    calculateFare(fromCity, stopName, passengerType);
  };

  const calculateFare = (from, to, type) => {
    // Basic fare calculation logic - you can enhance this
    let baseFare = 10.00;

    if (from && to && from !== to) {
      // Calculate distance-based fare (placeholder logic)
      baseFare = 15.00;
    }

    switch (type) {
      case 'child':
        setFareAmount((baseFare * 0.5).toFixed(2));
        break;
      case 'adult':
        setFareAmount(baseFare.toFixed(2));
        break;
      case 'buspass':
        setFareAmount('0.00');
        break;
      default:
        setFareAmount(baseFare.toFixed(2));
    }
  };

  const handlePassengerTypePress = (type) => {
    setPassengerType(type);
    calculateFare(fromCity, toCity, type);
  };

  const handlePrintPress = () => {
    Alert.alert(
      t('ticket.alerts.printTitle'),
      t('ticket.alerts.printMessage'),
      [
        { text: t('common.ok'), style: 'default' }
      ]
    );
  };

  const handleLuggagePress = () => {
    Alert.alert(
      t('ticket.alerts.luggageTitle'),
      t('ticket.alerts.luggageMessage'),
      [
        { text: t('common.ok'), style: 'default' }
      ]
    );
  };

  const handleCancelPress = () => {
    Alert.alert(
      t('ticket.alerts.cancelTitle'),
      t('ticket.alerts.cancelMessage'),
      [
        { text: t('common.cancel'), style: 'cancel' },
        { text: t('common.confirm'), onPress: () => navigation.goBack() }
      ]
    );
  };

  // Handle scroll events for dropdown scrollbars
  const handleFromDropdownScroll = (event) => {
    const { contentOffset, contentSize, layoutMeasurement } = event.nativeEvent;
    const scrollPercentage = contentOffset.y / (contentSize.height - layoutMeasurement.height);
    setFromScrollPosition(Math.max(0, Math.min(1, scrollPercentage || 0)));
  };

  const handleToDropdownScroll = (event) => {
    const { contentOffset, contentSize, layoutMeasurement } = event.nativeEvent;
    const scrollPercentage = contentOffset.y / (contentSize.height - layoutMeasurement.height);
    setToScrollPosition(Math.max(0, Math.min(1, scrollPercentage || 0)));
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={handleBackPress}>
          <FontAwesome6 name="chevron-left" size={24} color={theme.textColor} />
        </TouchableOpacity>

        <Text style={styles.headerTitle}>{t('ticket.title')}</Text>

        <ColorModeToggle />
      </View>

      {/* Main Content Area */}
      <View style={styles.mainContentArea}>
        {/* Combined Card for Fare Amount and Dropdowns */}
        <View style={styles.fareAndDropdownCard}>
          {/* Fare Amount */}
          <View style={styles.fareContainer}>
            <Text style={styles.fareLabel}>{t('ticket.fareAmount')} :</Text>
            <Text style={styles.fareAmount}>{fareAmount} RS</Text>
          </View>

          {/* From Dropdown */}
          <View style={styles.dropdownContainer}>
            <TouchableOpacity
              style={styles.dropdown}
              onPress={() => setShowFromDropdown(!showFromDropdown)}
            >
              <Text style={styles.dropdownText}>
                {fromCity || t('ticket.fromPlaceholder')}
              </Text>
              <FontAwesome6
                name={showFromDropdown ? "chevron-up" : "chevron-down"}
                size={16}
                color={theme.text}
              />
            </TouchableOpacity>
            {showFromDropdown && (
              <View style={styles.floatingDropdownContainer}>
                <View style={styles.floatingDropdown}>
                  <ScrollView
                    style={styles.dropdownScrollView}
                    showsVerticalScrollIndicator={false}
                    nestedScrollEnabled={true}
                    onScroll={handleFromDropdownScroll}
                    scrollEventThrottle={16}
                  >
                    {cities.map((city, index) => (
                      <TouchableOpacity
                        key={index}
                        style={styles.dropdownItem}
                        onPress={() => handleFromCitySelect(city)}
                      >
                        <Text style={styles.dropdownItemText}>{city}</Text>
                      </TouchableOpacity>
                    ))}
                  </ScrollView>
                  {/* Custom Scrollbar with Grey Track and Gradient Thumb */}
                  <View style={styles.scrollbarTrack}>
                    <LinearGradient
                      colors={['#6777E8', '#95B2EC']}
                      style={[
                        styles.scrollbarThumb,
                        {
                          transform: [{
                            translateY: fromScrollPosition * (160 - 60) // Track height - thumb height
                          }]
                        }
                      ]}
                      start={{ x: 0, y: 0 }}
                      end={{ x: 0, y: 1 }}
                    />
                  </View>
                </View>
              </View>
            )}
          </View>

          {/* To Dropdown */}
          <View style={styles.dropdownContainer}>
            <TouchableOpacity
              style={styles.dropdown}
              onPress={() => setShowToDropdown(!showToDropdown)}
            >
              <Text style={styles.dropdownText}>
                {toCity || t('ticket.toPlaceholder')}
              </Text>
              <FontAwesome6
                name={showToDropdown ? "chevron-up" : "chevron-down"}
                size={16}
                color={theme.text}
              />
            </TouchableOpacity>
            {showToDropdown && (
              <View style={styles.floatingDropdownContainer}>
                <View style={styles.floatingDropdown}>
                  <ScrollView
                    style={styles.dropdownScrollView}
                    showsVerticalScrollIndicator={false}
                    nestedScrollEnabled={true}
                    onScroll={handleToDropdownScroll}
                    scrollEventThrottle={16}
                  >
                    {cities.map((city, index) => (
                      <TouchableOpacity
                        key={index}
                        style={styles.dropdownItem}
                        onPress={() => handleToCitySelect(city)}
                      >
                        <Text style={styles.dropdownItemText}>{city}</Text>
                      </TouchableOpacity>
                    ))}
                  </ScrollView>
                  {/* Custom Scrollbar with Grey Track and Gradient Thumb */}
                  <View style={styles.scrollbarTrack}>
                    <LinearGradient
                      colors={['#6777E8', '#95B2EC']}
                      style={[
                        styles.scrollbarThumb,
                        {
                          transform: [{
                            translateY: toScrollPosition * (160 - 60) // Track height - thumb height
                          }]
                        }
                      ]}
                      start={{ x: 0, y: 0 }}
                      end={{ x: 0, y: 1 }}
                    />
                  </View>
                </View>
              </View>
            )}
          </View>
        </View>

        {/* Scrollable Quick Action Buttons with Flex Wrap */}
        <ScrollView
          style={styles.quickActionsScrollView}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.quickActionsContent}
        >
          <View style={styles.quickActionsWrapper}>
            {quickStops.map((stop, index) => (
              <View key={`${stop}-${index}`} style={styles.quickActionButtonContainer}>
                <LinearGradient
                  colors={['#6777E8', '#95B2EC']}
                  style={styles.quickActionGradientBorder}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                >
                  <TouchableOpacity
                    style={styles.quickActionButton}
                    onPress={() => handleQuickStopPress(stop)}
                  >
                    <Text style={styles.quickActionText}>{stop}</Text>
                  </TouchableOpacity>
                </LinearGradient>
              </View>
            ))}
          </View>
          {/* Enhanced Blur effect at bottom for disappearing effect */}
          <LinearGradient
            colors={[
              'transparent',
              mode === 'dark' ? 'rgba(0, 4, 15, 0.3)' : 'rgba(255, 255, 255, 0.3)',
              mode === 'dark' ? 'rgba(0, 4, 15, 0.7)' : 'rgba(255, 255, 255, 0.7)',
              mode === 'dark' ? '#00040F' : '#FFFFFF'
            ]}
            style={styles.blurEffect}
            start={{ x: 0, y: 0 }}
            end={{ x: 0, y: 1 }}
          />
        </ScrollView>

        {/* Separator Line with Blurred Ends */}
        <View style={styles.separatorContainer}>
          <LinearGradient
            colors={['transparent', theme.borderColor, 'transparent']}
            style={styles.separatorLineGradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
          />
        </View>
      </View>

      {/* Bottom Fixed Buttons */}
      <View style={styles.bottomButtonsContainer}>
        {/* Passenger Type Buttons */}
        <View style={styles.passengerTypeContainer}>
          {/* Child Button */}
          {passengerType === 'child' ? (
            <LinearGradient
              colors={['#6777E8', '#95B2EC']}
              style={[styles.passengerTypeButton, { flex: 1, marginHorizontal: 5 }]}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            >
              <TouchableOpacity
                style={styles.passengerTypeButtonInner}
                onPress={() => handlePassengerTypePress('child')}
              >
                <Text style={styles.passengerTypeTextActive}>
                  {t('ticket.passengerTypes.child')}
                </Text>
              </TouchableOpacity>
            </LinearGradient>
          ) : (
            <TouchableOpacity
              style={styles.passengerTypeButton}
              onPress={() => handlePassengerTypePress('child')}
            >
              <Text style={styles.passengerTypeText}>
                {t('ticket.passengerTypes.child')}
              </Text>
            </TouchableOpacity>
          )}

          {/* Adult Button */}
          {passengerType === 'adult' ? (
            <LinearGradient
              colors={['#6777E8', '#95B2EC']}
              style={[styles.passengerTypeButton, { flex: 1, marginHorizontal: 5 }]}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            >
              <TouchableOpacity
                style={styles.passengerTypeButtonInner}
                onPress={() => handlePassengerTypePress('adult')}
              >
                <Text style={styles.passengerTypeTextActive}>
                  {t('ticket.passengerTypes.adult')}
                </Text>
              </TouchableOpacity>
            </LinearGradient>
          ) : (
            <TouchableOpacity
              style={styles.passengerTypeButton}
              onPress={() => handlePassengerTypePress('adult')}
            >
              <Text style={styles.passengerTypeText}>
                {t('ticket.passengerTypes.adult')}
              </Text>
            </TouchableOpacity>
          )}

          {/* Bus Pass Button */}
          {passengerType === 'buspass' ? (
            <LinearGradient
              colors={['#6777E8', '#95B2EC']}
              style={[styles.passengerTypeButton, { flex: 1, marginHorizontal: 5 }]}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            >
              <TouchableOpacity
                style={styles.passengerTypeButtonInner}
                onPress={() => handlePassengerTypePress('buspass')}
              >
                <Text style={styles.passengerTypeTextActive}>
                  {t('ticket.passengerTypes.busPass')}
                </Text>
              </TouchableOpacity>
            </LinearGradient>
          ) : (
            <TouchableOpacity
              style={styles.passengerTypeButton}
              onPress={() => handlePassengerTypePress('buspass')}
            >
              <Text style={styles.passengerTypeText}>
                {t('ticket.passengerTypes.busPass')}
              </Text>
            </TouchableOpacity>
          )}
        </View>

        {/* Action Buttons */}
        <View style={styles.actionButtonsContainer}>
          <LinearGradient
            colors={['#6777E8', '#95B2EC']}
            style={styles.printButton}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          >
            <TouchableOpacity style={styles.printButtonInner} onPress={handlePrintPress}>
              <Text style={styles.printButtonText}>{t('ticket.buttons.print')}</Text>
            </TouchableOpacity>
          </LinearGradient>

          <View style={styles.bottomButtonsRow}>
            <TouchableOpacity style={styles.luggageButton} onPress={handleLuggagePress}>
              <Text style={styles.luggageButtonText}>{t('ticket.buttons.luggage')}</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.cancelButton} onPress={handleCancelPress}>
              <Text style={styles.cancelButtonText}>{t('ticket.buttons.cancel')}</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </View>
  );
}
