import React, { useState, useContext } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Alert
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { ThemeContext } from '../contexts/ThemeContext';
import { useTranslation } from 'react-i18next';
import FontAwesome6 from '@expo/vector-icons/FontAwesome6';
import ColorModeToggle from '../components/ColorModeToggle';
import createStyles from './Style_Schema/ticket_schema';

export default function TicketScreen() {
  const { theme } = useContext(ThemeContext);
  const styles = createStyles(theme);
  const { t } = useTranslation();
  const navigation = useNavigation();
  const route = useRoute();
  const busNumber = route.params?.busNumber;

  // State for ticket form
  const [fromCity, setFromCity] = useState('');
  const [toCity, setToCity] = useState('');
  const [passengerType, setPassengerType] = useState('adult'); // 'child', 'adult', 'buspass'
  const [fareAmount, setFareAmount] = useState('0.00');
  const [showFromDropdown, setShowFromDropdown] = useState(false);
  const [showToDropdown, setShowToDropdown] = useState(false);

  // Available cities
  const cities = ['Mayiladuthurai', 'Karaikal', 'Ambagathur', 'Nattarmbal', 'Sangar', 'Thiruvarur', 'Nagapattinam'];

  // Quick action stops
  const quickStops = ['Ambagathur', 'Nattarmbal', 'Sangar', 'Ambagathur'];

  const handleBackPress = () => {
    navigation.goBack();
  };

  const handleFromCitySelect = (city) => {
    setFromCity(city);
    setShowFromDropdown(false);
    calculateFare(city, toCity, passengerType);
  };

  const handleToCitySelect = (city) => {
    setToCity(city);
    setShowToDropdown(false);
    calculateFare(fromCity, city, passengerType);
  };

  const handleQuickStopPress = (stopName) => {
    setToCity(stopName);
    setShowToDropdown(false);
    calculateFare(fromCity, stopName, passengerType);
  };

  const calculateFare = (from, to, type) => {
    // Basic fare calculation logic - you can enhance this
    let baseFare = 10.00;

    if (from && to && from !== to) {
      // Calculate distance-based fare (placeholder logic)
      baseFare = 15.00;
    }

    switch (type) {
      case 'child':
        setFareAmount((baseFare * 0.5).toFixed(2));
        break;
      case 'adult':
        setFareAmount(baseFare.toFixed(2));
        break;
      case 'buspass':
        setFareAmount('0.00');
        break;
      default:
        setFareAmount(baseFare.toFixed(2));
    }
  };

  const handlePassengerTypePress = (type) => {
    setPassengerType(type);
    calculateFare(fromCity, toCity, type);
  };

  const handlePrintPress = () => {
    Alert.alert(
      t('ticket.alerts.printTitle'),
      t('ticket.alerts.printMessage'),
      [
        { text: t('common.ok'), style: 'default' }
      ]
    );
  };

  const handleLuggagePress = () => {
    Alert.alert(
      t('ticket.alerts.luggageTitle'),
      t('ticket.alerts.luggageMessage'),
      [
        { text: t('common.ok'), style: 'default' }
      ]
    );
  };

  const handleCancelPress = () => {
    Alert.alert(
      t('ticket.alerts.cancelTitle'),
      t('ticket.alerts.cancelMessage'),
      [
        { text: t('common.cancel'), style: 'cancel' },
        { text: t('common.confirm'), onPress: () => navigation.goBack() }
      ]
    );
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={handleBackPress}>
          <FontAwesome6 name="chevron-left" size={24} color={theme.textColor} />
        </TouchableOpacity>

        <Text style={styles.headerTitle}>{t('ticket.title')}</Text>

        <ColorModeToggle />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Fare Amount */}
        <View style={styles.fareContainer}>
          <Text style={styles.fareLabel}>{t('ticket.fareAmount')} :</Text>
          <Text style={styles.fareAmount}>{fareAmount} RS</Text>
        </View>

        {/* From Dropdown */}
        <View style={styles.dropdownContainer}>
          <Text style={styles.dropdownLabel}>{t('ticket.fromLabel')}</Text>
          <TouchableOpacity
            style={styles.dropdown}
            onPress={() => setShowFromDropdown(!showFromDropdown)}
          >
            <Text style={styles.dropdownText}>
              {fromCity || t('ticket.selectDeparture')}
            </Text>
            <FontAwesome6
              name={showFromDropdown ? "chevron-up" : "chevron-down"}
              size={16}
              color={theme.text}
            />
          </TouchableOpacity>
          {showFromDropdown && (
            <View style={styles.dropdownList}>
              {cities.map((city, index) => (
                <TouchableOpacity
                  key={index}
                  style={styles.dropdownItem}
                  onPress={() => handleFromCitySelect(city)}
                >
                  <Text style={styles.dropdownItemText}>{city}</Text>
                </TouchableOpacity>
              ))}
            </View>
          )}
        </View>

        {/* To Dropdown */}
        <View style={styles.dropdownContainer}>
          <Text style={styles.dropdownLabel}>{t('ticket.toLabel')}</Text>
          <TouchableOpacity
            style={styles.dropdown}
            onPress={() => setShowToDropdown(!showToDropdown)}
          >
            <Text style={styles.dropdownText}>
              {toCity || t('ticket.selectDestination')}
            </Text>
            <FontAwesome6
              name={showToDropdown ? "chevron-up" : "chevron-down"}
              size={16}
              color={theme.text}
            />
          </TouchableOpacity>
          {showToDropdown && (
            <View style={styles.dropdownList}>
              {cities.map((city, index) => (
                <TouchableOpacity
                  key={index}
                  style={styles.dropdownItem}
                  onPress={() => handleToCitySelect(city)}
                >
                  <Text style={styles.dropdownItemText}>{city}</Text>
                </TouchableOpacity>
              ))}
            </View>
          )}
        </View>

        {/* Quick Action Buttons */}
        <View style={styles.quickActionsContainer}>
          {quickStops.map((stop, index) => (
            <TouchableOpacity
              key={`${stop}-${index}`}
              style={styles.quickActionButton}
              onPress={() => handleQuickStopPress(stop)}
            >
              <Text style={styles.quickActionText}>{stop}</Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Passenger Type Buttons */}
        <View style={styles.passengerTypeContainer}>
          <TouchableOpacity
            style={[
              styles.passengerTypeButton,
              passengerType === 'child' && styles.passengerTypeButtonActive
            ]}
            onPress={() => handlePassengerTypePress('child')}
          >
            <Text style={[
              styles.passengerTypeText,
              passengerType === 'child' && styles.passengerTypeTextActive
            ]}>
              {t('ticket.passengerTypes.child')}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.passengerTypeButton,
              passengerType === 'adult' && styles.passengerTypeButtonActive
            ]}
            onPress={() => handlePassengerTypePress('adult')}
          >
            <Text style={[
              styles.passengerTypeText,
              passengerType === 'adult' && styles.passengerTypeTextActive
            ]}>
              {t('ticket.passengerTypes.adult')}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.passengerTypeButton,
              passengerType === 'buspass' && styles.passengerTypeButtonActive
            ]}
            onPress={() => handlePassengerTypePress('buspass')}
          >
            <Text style={[
              styles.passengerTypeText,
              passengerType === 'buspass' && styles.passengerTypeTextActive
            ]}>
              {t('ticket.passengerTypes.busPass')}
            </Text>
          </TouchableOpacity>
        </View>

        {/* Action Buttons */}
        <View style={styles.actionButtonsContainer}>
          <TouchableOpacity style={styles.printButton} onPress={handlePrintPress}>
            <Text style={styles.printButtonText}>{t('ticket.buttons.print')}</Text>
          </TouchableOpacity>

          <View style={styles.bottomButtonsRow}>
            <TouchableOpacity style={styles.luggageButton} onPress={handleLuggagePress}>
              <Text style={styles.luggageButtonText}>{t('ticket.buttons.luggage')}</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.cancelButton} onPress={handleCancelPress}>
              <Text style={styles.cancelButtonText}>{t('ticket.buttons.cancel')}</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </View>
  );
}
