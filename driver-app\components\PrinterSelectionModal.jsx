import React, { useState, useEffect, useContext } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  Alert,
  StyleSheet
} from 'react-native';
import { ThemeContext } from '../contexts/ThemeContext';
import { useTranslation } from 'react-i18next';
import FontAwesome6 from '@expo/vector-icons/FontAwesome6';
import { LinearGradient } from 'expo-linear-gradient';
import BluetoothPrinterService from '../services/BluetoothPrinterService';

export default function PrinterSelectionModal({ visible, onClose, onPrinterSelected }) {
  const { theme, mode } = useContext(ThemeContext);
  const { t } = useTranslation();
  const [devices, setDevices] = useState([]);
  const [isScanning, setIsScanning] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectedDevice, setConnectedDevice] = useState(null);

  useEffect(() => {
    if (visible) {
      checkCurrentConnection();
      startScan();
    } else {
      stopScan();
    }
  }, [visible]);

  const checkCurrentConnection = () => {
    const deviceInfo = BluetoothPrinterService.getConnectedDeviceInfo();
    setConnectedDevice(deviceInfo);
  };

  const startScan = async () => {
    setIsScanning(true);
    setDevices([]);
    
    const initialized = await BluetoothPrinterService.initialize();
    if (!initialized) {
      setIsScanning(false);
      return;
    }

    BluetoothPrinterService.scanForPrinters((device) => {
      setDevices(prevDevices => {
        const exists = prevDevices.find(d => d.id === device.id);
        if (!exists) {
          return [...prevDevices, device];
        }
        return prevDevices;
      });
    });

    // Stop scanning after 10 seconds
    setTimeout(() => {
      setIsScanning(false);
    }, 10000);
  };

  const stopScan = () => {
    BluetoothPrinterService.stopScan();
    setIsScanning(false);
  };

  const connectToDevice = async (device) => {
    setIsConnecting(true);
    try {
      await BluetoothPrinterService.connectToPrinter(device.id);
      setConnectedDevice({ id: device.id, name: device.name });
      onPrinterSelected(device);
      Alert.alert(
        'Success',
        `Connected to ${device.name}`,
        [{ text: 'OK', onPress: onClose }]
      );
    } catch (error) {
      Alert.alert(
        'Connection Failed',
        `Failed to connect to ${device.name}: ${error.message}`,
        [{ text: 'OK' }]
      );
    } finally {
      setIsConnecting(false);
    }
  };

  const disconnectDevice = async () => {
    try {
      await BluetoothPrinterService.disconnect();
      setConnectedDevice(null);
      Alert.alert('Disconnected', 'Printer disconnected successfully');
    } catch (error) {
      Alert.alert('Error', 'Failed to disconnect printer');
    }
  };

  const renderDeviceItem = ({ item }) => (
    <TouchableOpacity
      style={[styles.deviceItem, { 
        backgroundColor: theme.cardBackground,
        borderColor: theme.borderColor 
      }]}
      onPress={() => connectToDevice(item)}
      disabled={isConnecting}
    >
      <View style={styles.deviceInfo}>
        <FontAwesome6 
          name="print" 
          size={20} 
          color={theme.primary} 
          style={styles.deviceIcon}
        />
        <View style={styles.deviceDetails}>
          <Text style={[styles.deviceName, { color: theme.textColor }]}>
            {item.name || 'Unknown Device'}
          </Text>
          <Text style={[styles.deviceId, { color: theme.textSecondary }]}>
            {item.id}
          </Text>
        </View>
      </View>
      {connectedDevice?.id === item.id && (
        <FontAwesome6 
          name="check-circle" 
          size={20} 
          color={theme.success} 
        />
      )}
    </TouchableOpacity>
  );

  const styles = StyleSheet.create({
    modalOverlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'center',
      alignItems: 'center',
    },
    modalContent: {
      width: '90%',
      maxHeight: '80%',
      backgroundColor: theme.background,
      borderRadius: 15,
      padding: 20,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 20,
    },
    title: {
      fontSize: 18,
      fontWeight: 'bold',
      color: theme.textColor,
    },
    closeButton: {
      padding: 5,
    },
    scanButton: {
      borderRadius: 10,
      marginBottom: 15,
    },
    scanButtonInner: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 12,
      paddingHorizontal: 20,
    },
    scanButtonText: {
      color: 'white',
      fontWeight: 'bold',
      marginLeft: 8,
    },
    connectedSection: {
      marginBottom: 20,
      padding: 15,
      backgroundColor: theme.cardBackground,
      borderRadius: 10,
      borderColor: theme.success,
      borderWidth: 1,
    },
    connectedTitle: {
      fontSize: 16,
      fontWeight: 'bold',
      color: theme.success,
      marginBottom: 10,
    },
    connectedDevice: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    disconnectButton: {
      backgroundColor: theme.error,
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 6,
    },
    disconnectButtonText: {
      color: 'white',
      fontSize: 12,
      fontWeight: 'bold',
    },
    devicesList: {
      maxHeight: 300,
    },
    deviceItem: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      padding: 15,
      marginBottom: 10,
      borderRadius: 10,
      borderWidth: 1,
    },
    deviceInfo: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
    },
    deviceIcon: {
      marginRight: 12,
    },
    deviceDetails: {
      flex: 1,
    },
    deviceName: {
      fontSize: 16,
      fontWeight: 'bold',
      marginBottom: 2,
    },
    deviceId: {
      fontSize: 12,
    },
    scanningContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 20,
    },
    scanningText: {
      marginLeft: 10,
      color: theme.textSecondary,
    },
    emptyContainer: {
      alignItems: 'center',
      paddingVertical: 30,
    },
    emptyText: {
      color: theme.textSecondary,
      textAlign: 'center',
    },
    loadingOverlay: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.3)',
      justifyContent: 'center',
      alignItems: 'center',
      borderRadius: 15,
    },
  });

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          {/* Header */}
          <View style={styles.header}>
            <Text style={styles.title}>Select Printer</Text>
            <TouchableOpacity style={styles.closeButton} onPress={onClose}>
              <FontAwesome6 name="times" size={20} color={theme.textColor} />
            </TouchableOpacity>
          </View>

          {/* Connected Device Section */}
          {connectedDevice && (
            <View style={styles.connectedSection}>
              <Text style={styles.connectedTitle}>Connected Printer</Text>
              <View style={styles.connectedDevice}>
                <Text style={[styles.deviceName, { color: theme.textColor }]}>
                  {connectedDevice.name}
                </Text>
                <TouchableOpacity 
                  style={styles.disconnectButton}
                  onPress={disconnectDevice}
                >
                  <Text style={styles.disconnectButtonText}>Disconnect</Text>
                </TouchableOpacity>
              </View>
            </View>
          )}

          {/* Scan Button */}
          <LinearGradient
            colors={['#6777E8', '#95B2EC']}
            style={styles.scanButton}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          >
            <TouchableOpacity
              style={styles.scanButtonInner}
              onPress={startScan}
              disabled={isScanning}
            >
              <FontAwesome6 
                name={isScanning ? "spinner" : "search"} 
                size={16} 
                color="white" 
              />
              <Text style={styles.scanButtonText}>
                {isScanning ? 'Scanning...' : 'Scan for Printers'}
              </Text>
            </TouchableOpacity>
          </LinearGradient>

          {/* Devices List */}
          {isScanning && devices.length === 0 ? (
            <View style={styles.scanningContainer}>
              <ActivityIndicator size="small" color={theme.primary} />
              <Text style={styles.scanningText}>Searching for printers...</Text>
            </View>
          ) : devices.length > 0 ? (
            <FlatList
              data={devices}
              renderItem={renderDeviceItem}
              keyExtractor={(item) => item.id}
              style={styles.devicesList}
              showsVerticalScrollIndicator={false}
            />
          ) : (
            <View style={styles.emptyContainer}>
              <FontAwesome6 name="print" size={40} color={theme.textSecondary} />
              <Text style={styles.emptyText}>
                No printers found.{'\n'}Make sure your printer is on and in pairing mode.
              </Text>
            </View>
          )}

          {/* Loading Overlay */}
          {isConnecting && (
            <View style={styles.loadingOverlay}>
              <ActivityIndicator size="large" color="white" />
              <Text style={{ color: 'white', marginTop: 10 }}>Connecting...</Text>
            </View>
          )}
        </View>
      </View>
    </Modal>
  );
}
