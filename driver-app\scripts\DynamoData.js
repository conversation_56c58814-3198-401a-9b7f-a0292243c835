import AWS from 'aws-sdk';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { fetchBusDataFromAPI } from '../scripts/BusStopRestAPI';
import { useBusStore } from '../stores/busStore';
import { AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY, AWS_REGION } from '@env';

// Configure AWS SDK with credentials & region
AWS.config.update({
    accessKeyId: AWS_ACCESS_KEY_ID,
    secretAccessKey: AWS_SECRET_ACCESS_KEY,
  region: AWS_REGION
});

// Initialize DynamoDB DocumentClient
const dynamoDB = new AWS.DynamoDB.DocumentClient();

/**
 * Get item from DynamoDB by id.
 * @param {string} tableName - Name of the DynamoDB table.
 * @param {number|string} id - The id to look up.
 * @returns {Promise<object|null>} - The retrieved item or null if not found.
 */
async function getBusMasterData(tableName, id) {
  const params = {
    TableName: tableName,
    Key: { id: Number(id) }
  };

  try {
    const data = await dynamoDB.get(params).promise();
    return data.Item;
  } catch (error) {
    console.error("Error fetching data:", error);
    return null;
  }
}

/**
 * Update bus coordinates in DynamoDB
 * @param {string} busNumber - The bus number (id)
 * @param {object} coordinates - The coordinates object {latitude, longitude}
 *  * @param {object} busStopsData - Bus stops data (optional)
 * @param {object} busHistoryData - Bus history data (optional)
 * @returns {Promise<object>} - Object with status and lastStop flag
 */
async function updateBusLocation(busNumber, coordinates, busStopsData = null, busHistoryData = null) {
  // console.log("Bus Number:", busNumber);
  // console.log('Coordinates:', coordinates);
  // console.log('Bus Stops Data:', busStopsData);
  // console.log('Bus History Data:', busHistoryData);

  const params = {
    TableName: "Buses-DB",
    Key: {
      // Use 'busNumber' here, because that's the partition key in Buses-DB
      busNumber: String(busNumber),
    },
    UpdateExpression: "SET currentCoordinates = :coords",
    ExpressionAttributeValues: {
      ":coords": `${coordinates.latitude},${coordinates.longitude}`
    }
  };

  try {
    await dynamoDB.update(params).promise();
    console.log(`Updated location for bus ${busNumber}`);

    // Check if bus is near any stop and update history if needed
    const result = await checkAndUpdateBusStopHistory(busNumber, coordinates, busStopsData,
      busHistoryData);

    // Log the result for debugging
    if (result.isLastStop) {
      console.log(`Last stop reached for bus ${busNumber}, emitting event`);
    }

    return {
      success: true,
      isLastStop: result.isLastStop || false
    };
  } catch (error) {
    console.error("Error updating bus location:", error);
    return {
      success: false,
      isLastStop: false
    };
  }
}

/**
 * Get all bus stop coordinates from the busStops field in Buses-DB for a specific bus
 * @param {string} busNumber - The bus number to get stops for
 * @returns {Promise<Array<object>>} Array of bus stop objects
 */
async function getBusStops(busNumber) {
  const params = {
    TableName: "Buses-DB",
    Key: {
      busNumber: String(busNumber)
    }
  };
  try {
    const data = await dynamoDB.get(params).promise();

    if (!data.Item || !data.Item.busStops) {
      console.error(`No bus stops found for bus ${busNumber}`);
      return [];
    }

    // console.log(`Retrieved bus stops for bus ${busNumber}:`, JSON.stringify(data.Item.busStops));

    // Get the raw bus stops array - this should be a simple array of strings
    const busStops = data.Item.busStops;

    // Ensure we're working with an array
    if (!Array.isArray(busStops)) {
      console.error(`Bus stops data is not an array for bus ${busNumber}`);
      return [];
    }

    console.log("Bus stops array:", busStops);

    // Now fetch the coordinates for each stop from BusStops-DB
    const stopsWithCoordinates = await Promise.all(
      busStops.map(async (stopName) => {
        // Handle both string values and objects with S property
        const actualStopName = typeof stopName === 'string' ? stopName :
          (stopName.S ? stopName.S : String(stopName));

        // console.log(`Processing stop: ${actualStopName}`);
        const stopData = await fetchBusStopDetails(actualStopName);

        return {
          stopName: actualStopName,
          coordinates: stopData ? stopData.coordinates : "0,0" // Use actual coordinates or fallback
        };
      })
    );

    // console.log("Stops with coordinates:", stopsWithCoordinates);
    return stopsWithCoordinates;
  } catch (error) {
    console.error(`Error fetching bus stops for bus ${busNumber}:`, error);
    console.error("Error details:", error.stack);
    return [];
  }
}

/**
 * Get coordinates for a specific bus stop from BusStops-DB
 * @param {string} stopName - The name of the bus stop
 * @returns {Promise<object|null>} - The bus stop data or null if not found
 */
async function fetchBusStopDetails(stopName) {
  const params = {
    TableName: "BusStops-DB",
    Key: {
      stopName: String(stopName)
    }
  };

  try {
    const data = await dynamoDB.get(params).promise();
    return data.Item;
  } catch (error) {
    console.error(`Error fetching coordinates for stop ${stopName}:`, error);
    return null;
  }
}
/**
 * Check if bus is near a stop and update history if needed
 * @param {string} busNumber - The bus number
 * @param {object} currentCoordinates - Current coordinates {latitude, longitude}
 * @param {object} [busStopsData] - Optional cached bus stops data
 * @param {object} [busHistoryData] - Optional cached bus history data
 * @returns {Promise<object>} - Object with updated status and isLastStop flag
 */
async function checkAndUpdateBusStopHistory(busNumber, currentCoordinates, busStopsData = null,
  busHistoryData = null) {
  try {
    // Get current time and date once at the beginning of the function
    const now = new Date();
    const timeString = now.toTimeString().slice(0, 8); // HH:MM:SS format
    const today = now.toISOString().slice(0, 10); // YYYY-MM-DD format
    
    // Use provided bus stops if available, otherwise fetch from REST API
    let busStops = busStopsData;
    let busHistory = Array.isArray(busHistoryData) ? busHistoryData : [];
    // console.log("[caubsh][busStops:]", busStops);
    // console.log("[caubsh][busHistory:]", busHistory);

    if (!busStops || busStops.length === 0) {
      // console.log(`No bus stops provided for bus ${busNumber}, fetching from REST API`);
      const apiData = await fetchBusDataFromAPI(busNumber);
      // console.log("[caubsh][apiData:]", apiData);

      if (apiData && apiData.stops && Array.isArray(apiData.stops)) {
        // Use the already formatted stops data from the API
        busStops = apiData.stops;
        busHistory = apiData.history;
        // console.log(`Retrieved ${busStops.length} stops from API for bus ${busNumber}`);
      }
    } else {
      // console.log(`Using provided ${busStops.length} bus stops for bus ${busNumber}`);
    }

    if (!busStops || busStops.length === 0) {
      // console.log(`No stops found for bus ${busNumber} from any source`);
      return { updated: false, isLastStop: false };
    }

    // Pre-process all bus stop names once to avoid repeated toLowerCase() and trim() operations
    const normalizedStopNames = busStops.map(stop => ({
      normalizedName: stop.stopName.toLowerCase().trim(),
      originalStop: stop,
      index: busStops.indexOf(stop)
    }));

    // Create a map for faster lookups
    const stopNameToIndices = {};
    normalizedStopNames.forEach(item => {
      if (!stopNameToIndices[item.normalizedName]) {
        stopNameToIndices[item.normalizedName] = [];
      }
      stopNameToIndices[item.normalizedName].push(item.index);
    });

    // Convert current coordinates to numbers
    const currentLat = parseFloat(currentCoordinates.latitude);
    const currentLng = parseFloat(currentCoordinates.longitude);

    // Track which stops have been visited (both regular and redirected)
    const visitedStops = new Set();
    const redirectedStops = new Set(); 
    // Process history to determine which stops have been visited
    for (const entry of busHistory) {
      const parts = entry.split('#');
      if (parts.length >= 4) {
        const stopName = parts[0].toLowerCase().trim();
        const isRedirected = parts.includes("REDIRECTED") || parts.includes("Redirected or Data not found");

        // Count all stops regardless of date
        visitedStops.add(stopName);
        if (isRedirected) {
          redirectedStops.add(stopName);
        }
      }
    }

    // Find the last non-redirected stop in history
    let lastStopName = null;
    let lastStopIndex = -1;
    let lastStopTime = null;

    for (let i = busHistory.length - 1; i >= 0; i--) {
      const entry = busHistory[i];
      const parts = entry.split('#');

      // Skip redirected entries when finding the last actual stop
      if (parts.includes("REDIRECTED") || parts.includes("Redirected or Data not found")) continue;

      if (parts.length >= 3) {
        lastStopName = parts[0].toLowerCase().trim();
        lastStopTime = parts[2];

        // Check if we have the index stored in the history entry
        const idxPart = parts.find(part => part.startsWith("IDX"));
        if (idxPart) {
          lastStopIndex = parseInt(idxPart.substring(3));
          // console.log(`Using stored index ${lastStopIndex} from history for ${lastStopName}`);
        } else {
          // Fall back to finding the index in the route - use the normalized map for faster lookup
          const possibleIndices = stopNameToIndices[lastStopName] || [];
          if (possibleIndices.length > 0) {
            lastStopIndex = possibleIndices[0]; // Take the first occurrence
            // console.log(`Using first occurrence index ${lastStopIndex} from lookup map for ${lastStopName}`);
          }
        }

        break;
      }
    }

    // console.log(`Last recorded stop: ${lastStopName} at index ${lastStopIndex}`);

    // Find the nearest stop to current position
    let nearestStop = null;
    let nearestDistance = Infinity;
    let nearestStopIndex = -1;

    for (let i = 0; i < busStops.length; i++) {
      const stop = busStops[i];
      if (!stop.coordinates) continue;

      // Parse stop coordinates (format: "lat,lng")
      const [stopLat, stopLng] = stop.coordinates.split(',').map(coord => parseFloat(coord));
      
      // Calculate distance between current position and stop
      const distance = calculateDistance(currentLat, currentLng, stopLat, stopLng);

      // Track the nearest stop
      if (distance < nearestDistance) {
        nearestDistance = distance;
        nearestStop = stop;
        nearestStopIndex = i;
        
        // Early exit if we're already within the threshold distance (150 meters)
        if (nearestDistance <= 0.15) {
          // console.log(`Found stop within 150m threshold. Breaking distance calculation loop early.`);
          break;
        }
      }
    }

    // Check if bus is near any stop (within 150 meters)
    let anyStopUpdated = false;
    let isLastStop = false;

    // If we're near a stop (within 150 meters)
    if (nearestStop && nearestDistance <= 0.15) {
      // console.log(`Bus ${busNumber} is near stop: ${nearestStop.stopName} (${nearestDistance.toFixed(3)} km)`);

      const currentStopName = nearestStop.stopName.toLowerCase().trim();

      // Find all instances of the current stop in the route - use the preprocessed map
      const stopInstances = stopNameToIndices[currentStopName] || [];
      // console.log(`Found ${stopInstances.length} instances of stop "${currentStopName}" in the route`);

      // Determine which instance of the stop we're at based on the last recorded stop
      let mostLikelyStopIndex = nearestStopIndex;

      if (stopInstances.length > 1 && lastStopIndex !== -1) {
        // Check if we have a stored index in the history entry
        const idxPart = busHistory.length > 0 ?
          busHistory[busHistory.length - 1].split('#').find(part => part.startsWith("IDX")) : null;

        if (idxPart) {
          // If we have a stored index, use it to determine which instance we're at
          const storedIndex = parseInt(idxPart.substring(3));

          // If the stored index matches one of our instances, use that
          if (stopInstances.includes(storedIndex)) {
            mostLikelyStopIndex = storedIndex;
            // console.log(`Using stored index ${mostLikelyStopIndex} directly from history`);
          } else {
            // Otherwise, find the instance that comes after the last recorded stop
            let bestInstance = -1;
            let minDistance = busStops.length;

            for (const instance of stopInstances) {
              // Calculate forward distance from last stop to this instance
              let distance = 0;
              if (instance > lastStopIndex) {
                distance = instance - lastStopIndex;
              } else {
                // Handle wrap-around
                distance = busStops.length - lastStopIndex + instance;
              }

              if (distance < minDistance && distance > 0) {
                minDistance = distance;
                bestInstance = instance;
              }
            }

            if (bestInstance !== -1) {
              mostLikelyStopIndex = bestInstance;
              // console.log(`Adjusted stop index from ${nearestStopIndex} to ${mostLikelyStopIndex} based on route sequence`);
            }
          }
        } else {
          // Original logic for when we don't have a stored index
          let bestInstance = -1;
          let minDistance = busStops.length;

          // If this is the first entry in history, prefer the first instance (index 0)
          if (busHistory.length === 0 && stopInstances.length > 0) {
            // Get current time
            const now = new Date();
            const currentTime = now.toTimeString().slice(0, 8); // HH:MM:SS
            const currentTime24h = currentTime;
            const currentTimeSeconds = timeToSeconds(currentTime24h);

            // console.log(`First history entry with multiple instances of stop. Current time: ${currentTime}`);

            // Find the instance with the closest scheduled time
            let closestInstance = stopInstances[0];
            let minTimeDiff = Infinity;

            for (const instance of stopInstances) {
              if (instance >= 0 && instance < busStops.length) {
                const stopScheduledTime = busStops[instance].scheduledTime || busStops[instance].time;

                if (stopScheduledTime) {
                  const stopTime24h = convertTo24Hour(stopScheduledTime);
                  const stopTimeSeconds = timeToSeconds(stopTime24h);

                  // Calculate time difference
                  const timeDiff = Math.abs(stopTimeSeconds - currentTimeSeconds);
                  // console.log(`Instance ${instance} scheduled time: ${stopScheduledTime} (${stopTime24h}), diff: ${Math.floor(timeDiff / 60)} minutes`);

                  if (timeDiff < minTimeDiff) {
                    minTimeDiff = timeDiff;
                    closestInstance = instance;
                  }
                }
              }
            }

            mostLikelyStopIndex = closestInstance;
            // console.log(`First history entry, using instance at index ${mostLikelyStopIndex} based on scheduled time`);
          } else {
            for (const instance of stopInstances) {
              // Calculate forward distance from last stop to this instance
              let distance = 0;
              if (instance > lastStopIndex) {
                distance = instance - lastStopIndex;
              } else {
                // Handle wrap-around
                distance = busStops.length - lastStopIndex + instance;
              }

              if (distance < minDistance && distance > 0) {
                minDistance = distance;
                bestInstance = instance;
              }
            }

            if (bestInstance !== -1) {
              mostLikelyStopIndex = bestInstance;
              // console.log(`Adjusted stop index from ${nearestStopIndex} to ${mostLikelyStopIndex} based on route sequence`);
            }
          }
        }
      } else if (stopInstances.length > 1 && lastStopIndex === -1) {
        // If we have no history yet but multiple instances of this stop exist,
        // use the scheduled time to determine which instance is most likely

        // Get current time
    const now = new Date();
    const currentTime = now.toTimeString().slice(0, 8); // HH:MM:SS
        const currentTime24h = currentTime;
        const currentTimeSeconds = timeToSeconds(currentTime24h);

        // console.log(`No history yet with multiple instances of stop. Current time: ${currentTime}`);

        // Find the instance with the closest scheduled time
        let closestInstance = stopInstances[0];
        let minTimeDiff = Infinity;

        for (const instance of stopInstances) {
          if (instance >= 0 && instance < busStops.length) {
            const stopScheduledTime = busStops[instance].scheduledTime || busStops[instance].time;

            if (stopScheduledTime) {
              const stopTime24h = convertTo24Hour(stopScheduledTime);
              const stopTimeSeconds = timeToSeconds(stopTime24h);

              // Calculate time difference
              const timeDiff = Math.abs(stopTimeSeconds - currentTimeSeconds);
              // console.log(`Instance ${instance} scheduled time: ${stopScheduledTime} (${stopTime24h}), diff: ${Math.floor(timeDiff / 60)} minutes`);

              if (timeDiff < minTimeDiff) {
                minTimeDiff = timeDiff;
                closestInstance = instance;
              }
            }
          }
        }

        mostLikelyStopIndex = closestInstance;
        // console.log(`No history yet, using instance at index ${mostLikelyStopIndex} based on scheduled time (closest to current time)`);
      }

      // console.log(`Most likely stop index: ${mostLikelyStopIndex}`);

      // IDX BOUNDARY CHECK
      // If the determined index exceeds the bus stops array, ignore completely
      if (mostLikelyStopIndex >= busStops.length) {
        console.log(`[Boundary Check] Stop index ${mostLikelyStopIndex} exceeds bus stops array length (${busStops.length}). All trips completed. Ignoring.`);
        return { updated: false, isLastStop: false };
      }

      // TIME-BASED INSTANCE VALIDATION
      // Use arrival time from history to determine which instance (IDX) of the stop we should be at
      if (stopInstances.length > 1) {
        // Find the last recorded time for this stop name from history
        let lastRecordedTime = null;
        let lastRecordedIndex = -1;
        
        for (let i = busHistory.length - 1; i >= 0; i--) {
          const entry = busHistory[i];
          const parts = entry.split('#');
          
          if (parts.length >= 4) {
            const entryStopName = parts[0].toLowerCase().trim();
            const entryTime = parts[2]; // HH:MM:SS format
            
            if (entryStopName === currentStopName) {
              lastRecordedTime = entryTime;
              
              // Get the IDX if available
              const idxPart = parts.find(part => part.startsWith("IDX"));
              if (idxPart) {
                lastRecordedIndex = parseInt(idxPart.substring(3));
              }
              
              console.log(`[Time-Based] Found last entry for "${currentStopName}": ACTUAL at ${entryTime} (IDX: ${lastRecordedIndex})`);
              break;
            }
          }
        }
        
        if (lastRecordedTime && lastRecordedIndex !== -1) {
          // Convert current time and last recorded time to comparable format
          const now = new Date();
          const currentTimeStr = now.toTimeString().slice(0, 8); // HH:MM:SS
          const currentTimeSeconds = timeToSeconds(currentTimeStr);
          const lastRecordedTimeSeconds = timeToSeconds(lastRecordedTime);
          
                     // Calculate time difference (same day only)
           let timeDiff = currentTimeSeconds - lastRecordedTimeSeconds;
          
                     console.log(`[Time-Based] Last recorded "${currentStopName}" at ${lastRecordedTime} (IDX: ${lastRecordedIndex}), current time: ${currentTimeStr}`);
           console.log(`[Time-Based] Time difference: ${Math.floor(timeDiff / 3600)} hours ${Math.floor((timeDiff % 3600) / 60)} minutes`);
           
           // Check if enough time has passed since last recording
           if (timeDiff < 0) {
             console.log(`[Time-Based] Current time (${currentTimeStr}) is earlier than last recorded time (${lastRecordedTime}). Ignoring - time hasn't progressed.`);
             return { updated: false, isLastStop: false };
           }
           
           // Find the next appropriate instance based on time progression
           let appropriateIndex = -1;
           
           for (const instanceIndex of stopInstances) {
             if (instanceIndex > lastRecordedIndex) {
               // Check if this instance index is within bounds
               if (instanceIndex >= busStops.length) {
                 console.log(`[Time-Based] Instance IDX ${instanceIndex} exceeds bus stops array length (${busStops.length}). Ignoring.`);
                 continue;
               }
               
               // Check if enough time has passed for this instance
               const instanceScheduledTime = busStops[instanceIndex].scheduledTime || busStops[instanceIndex].time;
               if (instanceScheduledTime) {
                 const instanceTime24h = convertTo24Hour(instanceScheduledTime);
                 const instanceTimeSeconds = timeToSeconds(instanceTime24h);
                 
                 // If current time is close to or past this instance's scheduled time
                 const scheduleTimeDiff = Math.abs(currentTimeSeconds - instanceTimeSeconds);
                 if (scheduleTimeDiff <= 3600) { // Within 1 hour of scheduled time
                   appropriateIndex = instanceIndex;
                   console.log(`[Time-Based] Found appropriate instance at IDX ${instanceIndex} (scheduled: ${instanceScheduledTime})`);
                   break;
                 }
               }
             }
           }
           
           if (appropriateIndex !== -1) {
             mostLikelyStopIndex = appropriateIndex;
             console.log(`[Time-Based] Updated mostLikelyStopIndex to ${mostLikelyStopIndex} based on time progression`);
           } else {
             console.log(`[Time-Based] No appropriate instance found based on time. Current instance may be too early or all instances completed.`);
             return { updated: false, isLastStop: false };
           }
        }
      }

      // Get scheduled times from busStops array using indices
      let lastStopScheduledTime = null;
      let currentStopScheduledTime = null;

      // Get scheduled time for last stop from busStops array
      if (lastStopIndex !== -1 && lastStopIndex >= 0 && lastStopIndex < busStops.length) {
        lastStopScheduledTime = busStops[lastStopIndex].scheduledTime || busStops[lastStopIndex].time;
        // console.log(`Last stop (${busStops[lastStopIndex].stopName}) scheduled time from busStops: ${lastStopScheduledTime}`);
      }

      // Get scheduled time for current stop from busStops array
      if (mostLikelyStopIndex >= 0 && mostLikelyStopIndex < busStops.length) {
        currentStopScheduledTime = busStops[mostLikelyStopIndex].scheduledTime || busStops[mostLikelyStopIndex].time;
        // console.log(`Current stop (${busStops[mostLikelyStopIndex].stopName}) scheduled time from busStops: ${currentStopScheduledTime}`);
      }
      // IMPORTANT: Check scheduled times FIRST, before any other validation
      // This ensures we don't record stops that are too far ahead in the schedule
      if (lastStopIndex !== -1 && lastStopTime && lastStopScheduledTime && currentStopScheduledTime) {
        // Convert times to 24-hour format for proper comparison
        const currentStopTime24h = convertTo24Hour(currentStopScheduledTime);
        const lastStopTime24h = convertTo24Hour(lastStopScheduledTime);

        // console.log(`Current stop time (24h): ${currentStopTime24h}`);
        // console.log(`Last stop time (24h): ${lastStopTime24h}`);

        // Make sure we have valid time formats before calculating difference
        if (currentStopTime24h && lastStopTime24h) {
          // Calculate the scheduled time difference between stops
          const currentTimeSeconds = timeToSeconds(currentStopTime24h);
          const lastTimeSeconds = timeToSeconds(lastStopTime24h);

          // console.log(`Current time in seconds: ${currentTimeSeconds}`);
          // console.log(`Last time in seconds: ${lastTimeSeconds}`);

          const scheduledTimeDiff = Math.abs(currentTimeSeconds - lastTimeSeconds);

          // console.log(`Scheduled time difference: ${scheduledTimeDiff}`);

          // Log the time difference in a readable format
          // console.log(`Scheduled time difference: ${Math.floor(scheduledTimeDiff / 60)} minutes and ${scheduledTimeDiff % 60} seconds`);
          // Calculate the sequence distance between the stops
          let sequenceDistance = 0;
          if (mostLikelyStopIndex >= lastStopIndex) {
            sequenceDistance = mostLikelyStopIndex - lastStopIndex;
          } else {
            // Handle wrap-around case
            sequenceDistance = busStops.length - lastStopIndex + mostLikelyStopIndex;
          }

          // console.log(`Sequence distance between stops: ${sequenceDistance}`);

          // If scheduled time difference is more than 1 hour AND the sequence distance is large, skip this stop
          // For sequential stops (distance of 1), allow any time difference
          if (scheduledTimeDiff > 3600 && sequenceDistance > 1) { // 3600 seconds = 1 hour, allow up to 2 stops in sequence
            // console.log(`Scheduled time difference between ${nearestStop.stopName} and last stop is ${Math.floor(scheduledTimeDiff / 60)} minutes with sequence distance ${sequenceDistance}. Skipping to avoid out-of-sequence recording.`);
            return { updated: false, isLastStop: false };
          } else if (scheduledTimeDiff > 3600) {
            // console.log(`Large time difference (${Math.floor(scheduledTimeDiff / 60)} minutes) but stops are sequential (distance: ${sequenceDistance}). Allowing recording.`);
          }
        } else {
          // console.log(`Invalid time format detected: currentStopTime24h=${currentStopTime24h}, lastStopTime24h=${lastStopTime24h}`);
        }
      } else {
        // console.log(`Unable to validate scheduled times: lastStopScheduledTime=${lastStopScheduledTime}, currentStopScheduledTime=${currentStopScheduledTime}`);
      }
      // Debug the scheduled time validation condition
      // console.log(`Scheduled time validation check:
      //   - lastStopIndex: ${lastStopIndex}
      //   - lastStopTime: ${lastStopTime}
      //   - lastStopScheduledTime: ${lastStopScheduledTime}
      //   - currentStopScheduledTime: ${currentStopScheduledTime}
      //   - lastStopIndex >= 0: ${lastStopIndex >= 0}
      //   - lastStopIndex < busStops.length: ${lastStopIndex < busStops.length}
      //   - busStops[lastStopIndex] exists: ${Boolean(busStops[lastStopIndex])}
      // `);
      // Now handle the general time difference check
      if (lastStopIndex !== -1 && lastStopTime) {
        const now = new Date();
        const currentTime = now.toTimeString().slice(0, 8); // HH:MM:SS

        // Calculate time difference in seconds
        let timeDiff = Math.abs(timeToSeconds(currentTime) - timeToSeconds(lastStopTime));

        // Handle day change (when current time is less than last recorded time)
        if (timeDiff > 43200) { // More than 12 hours difference likely means day change
          timeDiff = 86400 - timeDiff; // Adjust for day change
        }
      }

      // Improved last stop detection logic
      // Count unique stops visited in the current journey
      const uniqueStopsVisited = new Set();

      // Process history to count unique stops visited
      for (const entry of busHistory) {
      const parts = entry.split('#');
        if (parts.length >= 4) {
          const stopName = parts[0].toLowerCase().trim();
          uniqueStopsVisited.add(stopName);
        }
      }

      // Add the current stop we're at (if not already counted)
      uniqueStopsVisited.add(currentStopName);
      // console.log("uniqueStopsVisited:", uniqueStopsVisited);

      // Check if we're at the last stop in sequence
      // Option 1: Strict check - require all stops to be visited
      // isLastStop = (mostLikelyStopIndex === busStops.length - 1) && 
      // (uniqueStopsVisited.size >= busStops.length);

      // Option 2: Less strict - just check if we're at the last stop in sequence
      isLastStop = (mostLikelyStopIndex === busStops.length - 1);

      if (isLastStop) {
        // console.log(`Bus ${busNumber} has reached the last stop: ${nearestStop.stopName}`);
        // console.log(`Visited ${uniqueStopsVisited.size}/${busStops.length} unique stops. Confirmed as last stop.`);
      } else if (mostLikelyStopIndex === busStops.length - 1) {
        // console.log(`At last stop in sequence but only visited ${uniqueStopsVisited.size}/${busStops.length} unique stops. Not treating as last stop yet.`);
      }

      // Skip if this is the same as the last recorded stop and it was recorded recently (within 5 minutes)
      if (lastStopName === currentStopName && lastStopTime) {
        const now = new Date();
        const currentTime = now.toTimeString().slice(0, 8); // HH:MM:SS

        // Calculate time difference in seconds
        let timeDiff = Math.abs(timeToSeconds(currentTime) - timeToSeconds(lastStopTime));

        // Handle day change (when current time is less than last recorded time)
        if (timeDiff > 43200) { // More than 12 hours difference likely means day change
          timeDiff = 86400 - timeDiff; // Adjust for day change
        }

        // Use a consistent threshold (10 minutes = 600 seconds)
        const TIME_THRESHOLD = 900;

        if (timeDiff < TIME_THRESHOLD) {
          const minutes = Math.floor(timeDiff / 60);
          const seconds = timeDiff % 60;
          // console.log(`Stop ${nearestStop.stopName} was recorded recently (${minutes} minutes and ${seconds} seconds ago). Skipping to avoid duplicate entries.`);

          // Even if we're skipping recording, check if it's the last stop
          if (isLastStop) {
            // console.log("This is the last stop in the route. Notifying driver.");
            return { updated: false, isLastStop: true };
          }

          return { updated: false, isLastStop: false };
        } else {
          // console.log(`Last recorded at same stop was ${Math.floor(timeDiff / 60)} minutes and ${timeDiff % 60} seconds ago. Will record new entry.`);
        }
      }

      // Special case: First history entry
      if (busHistory.length === 0) {
        // If this is not the first stop in the route, mark previous stops as redirected
        if (mostLikelyStopIndex > 0) {
          // console.log(`First recorded stop is not the first stop in the route. Marking previous stops as redirected.`);
          // console.log(`Marking stops from index 0 to ${mostLikelyStopIndex - 1} as redirected`);

          // Create array to collect all redirected entries
          const redirectedEntries = [];
          
          // Track which indices we've already processed to avoid duplicates
          const processedIndices = new Set();

          // Only mark stops that haven't been visited yet
          for (let i = 0; i < mostLikelyStopIndex; i++) {
            // Skip if we've already processed this index
            if (processedIndices.has(i)) {
              // console.log(`Stop at index ${i} already processed. Skipping.`);
              continue;
            }

            const skippedStop = busStops[i];
            if (!skippedStop) {
              // console.log(`No stop data at index ${i}. Skipping.`);
              continue;
            }

            const skippedStopName = skippedStop.stopName;
            // console.log(`Processing skipped stop: ${skippedStopName} at index ${i}`);

            // Format: "stopName#REDIRECTED#time#date#IDXi"
            const redirectEntry = `${skippedStopName}#Redirected or Data not found#${timeString}#${today}#IDX${i}`;

            // Add to our batch collection instead of individual updates
            redirectedEntries.push(redirectEntry);
            
            // Mark this index as processed
            processedIndices.add(i);
          }

          // Send all redirected entries in a single batch update
          if (redirectedEntries.length > 0) {
            // console.log(`Batch recording ${redirectedEntries.length} redirected stops`);
            try {
              // First update the local busHistory array with these entries
              busHistory.push(...redirectedEntries);
              
              // Then use the batch update function to save to AWS
              const batchSuccess = await batchUpdateBusHistory(busNumber, busHistory, true, busStops.length);
              if (batchSuccess) {
                // console.log(`Successfully batch recorded ${redirectedEntries.length} redirected stops`);
                
                // Update visited stops set with all the redirected stops
                for (const entry of redirectedEntries) {
                  const parts = entry.split('#');
                  if (parts.length >= 1) {
                    const stopName = parts[0].toLowerCase().trim();
                    visitedStops.add(stopName);
                    redirectedStops.add(stopName);
                  }
                }
              } else {
                console.error(`Failed to batch record redirected stops`);
              }
            } catch (error) {
              console.error(`Error batch recording redirected stops:`, error);
            }
          }
        }
      }// If we have history and the current stop is not the expected next stop
      else if (lastStopIndex !== -1) {
        // Calculate the expected next stop index
        const expectedNextIndex = (lastStopIndex + 1) % busStops.length;

        // If we're not at the expected next stop, we need to mark skipped stops
        if (mostLikelyStopIndex !== expectedNextIndex) {
          // console.log(`Expected next stop index ${expectedNextIndex}, but found at ${mostLikelyStopIndex}`);

          // Determine the correct sequence of stops to mark as redirected
          // Only handle simple forward movement
          let skipIndices = [];

          if (mostLikelyStopIndex > lastStopIndex) {
            // Simple forward movement - mark skipped stops
            for (let i = lastStopIndex + 1; i < mostLikelyStopIndex; i++) {
              skipIndices.push(i);
            }
            console.log(`[Forward-Only] Marking ${skipIndices.length} skipped stops between ${lastStopIndex} and ${mostLikelyStopIndex}`);
          }
          // Note: We don't handle any other cases since backward movement is blocked by forward-only validation

          // Create array to collect all redirected entries
          const redirectedEntries = [];

          // Record all skipped stops in sequence
          for (const idx of skipIndices) {
            const skippedStop = busStops[idx];
            const skippedStopName = skippedStop.stopName.toLowerCase().trim();
            // We need to check if this specific instance of the stop has been visited
            // For stops that appear multiple times in the route, we need to be more careful
            let shouldSkip = false;
            // Check history to see if this specific instance (by index) has been recorded
            for (const entry of busHistory) {
              const parts = entry.split('#');
              
              // Check if this entry has an index marker
              const idxPart = parts.find(part => part.startsWith("IDX"));
              if (idxPart) {
                const entryIndex = parseInt(idxPart.substring(3));
                // If this exact index was already recorded, skip it
                if (entryIndex === idx) {
                  shouldSkip = true;
                  // console.log(`Stop ${skippedStop.stopName} at index ${idx} already recorded. Skipping.`);
                  break;
                }
              }
            }

            // Skip if we determined this specific instance should be skipped
            if (shouldSkip) continue;

            // console.log(`Bus ${busNumber} skipped stop: ${skippedStop.stopName}`);

            // Format: "stopName-REDIRECTED-time-date"
            const redirectEntry = `${skippedStop.stopName}#Redirected or Data not found#${timeString}#${today}#IDX${idx}`;

            // Add to our batch collection instead of individual updates
            redirectedEntries.push(redirectEntry);
            visitedStops.add(skippedStopName);
          }

          // Send all redirected entries in a single batch update
          if (redirectedEntries.length > 0) {
            // console.log(`Batch recording ${redirectedEntries.length} redirected stops`);
            try {
              // First update the local busHistory array with these entries
              busHistory.push(...redirectedEntries);
              
              // Then use the batch update function to save to AWS
              const batchSuccess = await batchUpdateBusHistory(busNumber, busHistory, true, busStops.length);
              if (batchSuccess) {
                // console.log(`Successfully batch recorded ${redirectedEntries.length} redirected stops`);
                
                // Update visited stops set with all the redirected stops
                for (const entry of redirectedEntries) {
                  const parts = entry.split('#');
                  if (parts.length >= 1) {
                    const stopName = parts[0].toLowerCase().trim();
                    visitedStops.add(stopName);
                    redirectedStops.add(stopName);
                  }
                }
              } else {
                console.error(`Failed to batch record redirected stops`);
              }
            } catch (error) {
              console.error(`Error batch recording redirected stops:`, error);
            }
          }
        }
      }

      // Check if we've completed a full circuit of the route - use normalized stop names
      const allStopsVisited = normalizedStopNames.every(item => 
        visitedStops.has(item.normalizedName)
      );

      // If all stops have been visited, we can record the current stop again
      // even if it's been visited before
      if (allStopsVisited) {
        // console.log("All stops have been visited. Starting a new circuit.");
        // Reset the visited stops tracking for the new circuit
        visitedStops.clear();
      }

      // Check if the current stop has already been visited and we haven't completed a circuit
      // Allow recording if the stop was previously only marked as redirected
      if (visitedStops.has(currentStopName) && !redirectedStops.has(currentStopName) && !allStopsVisited) {
        // Check if this specific instance (by index) has been recorded
        let thisInstanceRecorded = false;

        // Look through history to see if this specific instance (by index) has been recorded
        for (const entry of busHistory) {
          const parts = entry.split('#');
          const entryStopName = parts[0].toLowerCase().trim();

          // Check if this entry has an index marker matching our current instance
          const idxPart = parts.find(part => part.startsWith("IDX"));
          if (idxPart && entryStopName === currentStopName) {
            const entryIndex = parseInt(idxPart.substring(3));
            // If this exact index was already recorded, mark it
            if (entryIndex === mostLikelyStopIndex) {
              thisInstanceRecorded = true;
              break;
            }
          }
        }

        if (thisInstanceRecorded) {
          // console.log(`Stop ${nearestStop.stopName} at index ${mostLikelyStopIndex} already visited. Skipping recording.`);
        } else {
          // console.log(`Stop ${nearestStop.stopName} appeared before, but this is a different instance (index ${mostLikelyStopIndex}). Recording it.`);
          // Continue to record this instance below
          const historyEntry = `${nearestStop.stopName}#${currentLat},${currentLng}#${timeString}#${today}#IDX${mostLikelyStopIndex}`;

          // Update history in DynamoDB
          // console.log(`[checkAndUpdate] PRE-CALL updateBusHistory for current: ${nearestStop.stopName}`);
          // console.log(`[checkAndUpdate] History Entry: ${historyEntry}`);
          const updated = await updateBusHistory(busNumber, historyEntry, true, busHistory, busStops.length);
          // console.log(`[checkAndUpdate] POST-CALL updateBusHistory for current: ${nearestStop.stopName}, Result: ${updated}`);
          if (updated) {
            anyStopUpdated = true;
            visitedStops.add(currentStopName);
            // console.log(`[checkAndUpdate] Successfully recorded stop: ${nearestStop.stopName} at index ${mostLikelyStopIndex}`);

            // If this is the last stop, notify the driver
            if (isLastStop) {
              // console.log("This is the last stop in the route. Notifying driver.");
            }
          }
        }
      } else {
        // Now record the current stop
        const historyEntry = `${nearestStop.stopName}#${currentLat},${currentLng}#${timeString}#${today}#IDX${mostLikelyStopIndex}`;

        // Check if the last recorded entry was for the same stop
        let isDuplicate = false;
        if (busHistory.length > 0) {
          const lastEntry = busHistory[busHistory.length - 1];
          const lastParts = lastEntry.split('#');

          if (lastParts.length >= 1) {
            const lastStopName = lastParts[0].toLowerCase().trim();

            // If the last recorded stop is the same as current stop, don't record again
            if (lastStopName === currentStopName) {
              // console.log(`Last recorded stop was also ${nearestStop.stopName}. Skipping duplicate recording.`);
              isDuplicate = true;

              // Even if we're skipping recording, check if it's the last stop
      if (isLastStop) {
                // console.log("This is the last stop in the route. Notifying driver.");
                return { updated: false, isLastStop: true };
              }
            }
          }
        }

        // Only update history if it's not a duplicate of the last entry
        if (!isDuplicate) {
          // Update history in DynamoDB - pass true to skip the time-based duplicate check
          // since we've already done our own check based on the stop name
          // console.log(`[checkAndUpdate] PRE-CALL updateBusHistory for current: ${nearestStop.stopName}`); // More specific log
          // console.log(`[checkAndUpdate] History Entry: ${historyEntry}`); // Log the entry
          const updated = await updateBusHistory(busNumber, historyEntry, true, busHistory, busStops.length);
          // console.log(`[checkAndUpdate] POST-CALL updateBusHistory for current: ${nearestStop.stopName}, Result: ${updated}`); // More specific log
          if (updated) {
            anyStopUpdated = true;
            visitedStops.add(currentStopName);
            // console.log(`[checkAndUpdate] Successfully recorded stop: ${nearestStop.stopName} at index ${mostLikelyStopIndex}`);

            // If this is the last stop, notify the driver
            if (isLastStop) {
              // console.log("This is the last stop in the route. Notifying driver.");
            }
          }
        }
      }
    }

    // Return both whether a stop was updated and if it's the last stop
    return {
      updated: anyStopUpdated,
      isLastStop: isLastStop
    };
  } catch (error) {
    console.error(`Error checking bus stop history for bus ${busNumber}:`, error);
    console.error(`[checkAndUpdateBusStopHistory Error] Message: ${error.message}`); // Added detail
    console.error(`[checkAndUpdateBusStopHistory Error] Stack: ${error.stack}`);
    return { updated: false, isLastStop: false, error: error };
  }
}

// Helper function to convert 12-hour time format to 24-hour format
function convertTo24Hour(timeStr) {
  if (!timeStr) return null;

  // If already in 24-hour format (no am/pm), return as is
  if (!timeStr.toLowerCase().includes('am') && !timeStr.toLowerCase().includes('pm')) {
    return timeStr;
  }

  let [time, modifier] = timeStr.split(' ');

  // If no space between time and AM/PM, try to extract differently
  if (!modifier) {
    if (timeStr.toLowerCase().includes('am')) {
      time = timeStr.toLowerCase().replace('am', '').trim();
      modifier = 'AM';
    } else if (timeStr.toLowerCase().includes('pm')) {
      time = timeStr.toLowerCase().replace('pm', '').trim();
      modifier = 'PM';
    }
  }

  if (!modifier) {
    // If still no modifier, assume it's already in 24-hour format
    return time;
  }

  let [hours, minutes] = time.split(':');

  // Convert hours to number for calculation
  hours = parseInt(hours, 10);

  // Convert to 24-hour format
  if (modifier.toLowerCase() === 'pm' && hours < 12) {
    hours += 12;
  } else if (modifier.toLowerCase() === 'am' && hours === 12) {
    hours = 0;
  }

  // Format back to string with leading zeros
  hours = hours.toString().padStart(2, '0');

  // If minutes is undefined, set it to "00"
  if (!minutes) minutes = "00";

  return `${hours}:${minutes}`;
}

// Also update the updateBusHistory function to handle the new format
async function updateBusHistory(busNumber, historyEntry, skipDuplicateCheck = false, busHistory = [], busStopsCount = null) {
  console.log(`[updateBusHistory] Function called for bus ${busNumber} with entry: ${historyEntry}`); // Log entry
  try {
    // Extract stop name from the history entry
    const parts = historyEntry.split('#');
    if (parts.length < 4) {
      console.error(`[updateBusHistory] Invalid history entry format: ${historyEntry}`); // Added prefix
      return false;
    }
    console.log(`[updateBusHistory] Attempting GetCommand for bus ${busNumber}`);
    // Retrieve current history from DynamoDB
    // const getParams = {
    //   TableName: "Buses-DB",
    //   Key: { busNumber: String(busNumber) },
    //   ProjectionExpression: "history"
    // };

    // const data = await dynamoDB.get(getParams).promise();
    let currentHistory = busHistory;


    // Check if this is a duplicate of the last entry (within 2 minutes)
    if (!skipDuplicateCheck && currentHistory.length > 0) {
      const lastEntry = currentHistory[currentHistory.length - 1];
      const lastParts = lastEntry.split('#');
      const currentParts = historyEntry.split('#');
      
      if (lastParts.length >= 3 && currentParts.length >= 3) {
        const lastStopName = lastParts[0].toLowerCase().trim();
        const currentStopName = currentParts[0].toLowerCase().trim();
        const lastTime = lastParts[2];
        const currentTime = currentParts[2];
        // Extract IDX values if they exist
        const lastIdxPart = lastParts.find(part => part.startsWith("IDX"));
        const currentIdxPart = currentParts.find(part => part.startsWith("IDX"));
        // Get the index values
        const lastIdx = lastIdxPart ? parseInt(lastIdxPart.substring(3)) : -1;
        const currentIdx = currentIdxPart ? parseInt(currentIdxPart.substring(3)) : -1;

        // If same stop, same index, and within 2 minutes, skip
        const timeDiff = Math.abs(timeToSeconds(lastTime) - timeToSeconds(currentTime));
        if (lastStopName === currentStopName &&
          (lastIdx === currentIdx) &&
          timeDiff < 120) {
          console.log(`Duplicate entry detected for ${currentStopName} (IDX: ${currentIdx}) within ${Math.floor(timeDiff / 60)} minutes and ${timeDiff % 60} seconds. Skipping.`);
            return false;
        }
      }
    }

    // Check if adding this entry would exceed the bus stops count
    if (busStopsCount !== null && currentHistory.length >= busStopsCount) {
      console.log(`[updateBusHistory] History length (${currentHistory.length}) would exceed bus stops count (${busStopsCount}). Starting new circuit by clearing history.`);
      // Clear history to start a new circuit
      currentHistory = [];
    }

    // Append the new history entry
    currentHistory.push(historyEntry);

    // Update the DynamoDB record with the new history array
    const updateParams = {
      TableName: "Buses-DB",
      Key: { busNumber: String(busNumber) },
      UpdateExpression: "SET history = :history",
      ExpressionAttributeValues: {
        ":history": currentHistory
      }
    };

    await dynamoDB.update(updateParams).promise();
    console.log(`Updated history for bus ${busNumber}: ${historyEntry}`);
    // Update Zustand store
    if (typeof useBusStore !== 'undefined') {
      const { setBusHistory, busData } = useBusStore.getState();
      setBusHistory(busNumber, currentHistory);
      if (busData) {
        const updatedBusData = {
          ...busData,
          history: currentHistory
        };
        useBusStore.setState({ busData: updatedBusData });
      }
    }

    // Update AsyncStorage
    try {
      await AsyncStorage.setItem(`busHistory_${busNumber}`, JSON.stringify(currentHistory));
      console.log(`Updated AsyncStorage for bus ${busNumber}`);
    } catch (error) {
      console.error('Error updating AsyncStorage:', error);
    }

    return true;
  } catch (error) {
    console.error("[updateBusHistory] Error during update:", error); // Added prefix
    console.error(`[updateBusHistory Error] Message: ${error.message}`);
    console.error(`[updateBusHistory Error] Stack: ${error.stack}`);
    return false;
  }
}

/**
 * Batch update the bus history with multiple entries in a single AWS request
 * @param {string} busNumber - The bus number
 * @param {Array<string>} historyData - Array of history entries or complete history array
 * @param {boolean} isCompleteHistory - Whether historyData is the complete history (true) or just new entries (false)
 * @returns {Promise<boolean>} - Success status
 */
async function batchUpdateBusHistory(busNumber, historyData, isCompleteHistory = true, busStopsCount = null) {
  console.log(`[batchUpdateBusHistory] Function called for bus ${busNumber} with ${historyData.length} entries`);
  
  try {
    if (!historyData || !Array.isArray(historyData) || historyData.length === 0) {
      console.log(`No history data to update for bus ${busNumber}`);
      return true;
    }

    let updatedHistory = historyData;
    
    // If we're not given the complete history, we need to fetch current history first
    if (!isCompleteHistory) {
      console.log(`Fetching current history to add ${historyData.length} new entries`);
      
      // Use getBusHistory to retrieve current history from DynamoDB
      const historyData = await getBusHistory(busNumber);
      let currentHistory = [];
      
      // Handle history data from getBusHistory
      if (historyData && historyData.history) {
        if (Array.isArray(historyData.history)) {
          currentHistory = historyData.history;
        } else if (historyData.history.L) {
          // Extract string values from DynamoDB List format
          currentHistory = historyData.history.L.map(item =>
            item.S || String(item)
          );
        }
      }
      
      // Create a map of existing entries by index to check for duplicates
      const existingEntriesByIndex = {};
      currentHistory.forEach(entry => {
        const parts = entry.split('#');
        const idxPart = parts.find(part => part.startsWith("IDX"));
        if (idxPart) {
          const index = parseInt(idxPart.substring(3));
          existingEntriesByIndex[index] = entry;
        }
      });

      // Filter out any duplicates by index before adding
      const newEntries = historyData.filter(entry => {
        const parts = entry.split('#');
        const idxPart = parts.find(part => part.startsWith("IDX"));
        
        // If we can't determine the index, include it by default
        if (!idxPart) return true;
        
        const index = parseInt(idxPart.substring(3));
        // Include if the index doesn't exist in our current history
        return !existingEntriesByIndex[index];
      });

      if (newEntries.length === 0) {
        console.log(`All entries already exist in history for bus ${busNumber}`);
        return true;
      }

      // Append all new entries to current history
      updatedHistory = [...currentHistory, ...newEntries];
      console.log(`Adding ${newEntries.length} new entries to existing ${currentHistory.length} entries`);
    }

    // Check if the updated history exceeds the bus stops count
    if (busStopsCount !== null && updatedHistory.length > busStopsCount) {
      console.log(`[batchUpdateBusHistory] History length (${updatedHistory.length}) exceeds bus stops count (${busStopsCount}). Trimming to start new circuit.`);
      // Keep only the most recent entries up to the bus stops count
      updatedHistory = updatedHistory.slice(-busStopsCount);
      console.log(`[batchUpdateBusHistory] Trimmed history to ${updatedHistory.length} entries`);
    }

    // Update the DynamoDB record with the history in a single operation
    const updateParams = {
      TableName: "Buses-DB",
      Key: { busNumber: String(busNumber) },
      UpdateExpression: "SET history = :history",
      ExpressionAttributeValues: {
        ":history": updatedHistory
      }
    };

    await dynamoDB.update(updateParams).promise();
    console.log(`[batchUpdateBusHistory] Successfully updated history in DynamoDB for bus ${busNumber}`);
    
    // Update Zustand store if available
    if (typeof useBusStore !== 'undefined') {
      const { setBusHistory, busData } = useBusStore.getState();
      setBusHistory(busNumber, updatedHistory);
      if (busData) {
        const updatedBusData = {
          ...busData,
          history: updatedHistory
        };
        useBusStore.setState({ busData: updatedBusData });
      }
      console.log(`[batchUpdateBusHistory] Updated Zustand store for bus ${busNumber}`);
    }
    
    // Update AsyncStorage
    try {
      await AsyncStorage.setItem(`busHistory_${busNumber}`, JSON.stringify(updatedHistory));
      console.log(`[batchUpdateBusHistory] Updated AsyncStorage for bus ${busNumber}`);
    } catch (storageError) {
      console.error('[batchUpdateBusHistory] Error updating AsyncStorage:', storageError);
      // Continue even if AsyncStorage update fails
    }
    
    return true;
  } catch (error) {
    console.error(`[batchUpdateBusHistory] Error during update:`, error);
    console.error(`[batchUpdateBusHistory Error] Message: ${error.message}`);
    console.error(`[batchUpdateBusHistory Error] Stack: ${error.stack}`);
    return false;
  }
}

// Helper function to convert HH:MM:SS to seconds
function timeToSeconds(timeStr) {
  if (!timeStr) return 0;

  try {
    // Handle both HH:MM:SS and HH:MM formats
    const parts = timeStr.split(':');
    let hours = 0, minutes = 0, seconds = 0;

    if (parts.length >= 3) {
      // HH:MM:SS format
      hours = parseInt(parts[0], 10) || 0;
      minutes = parseInt(parts[1], 10) || 0;
      seconds = parseInt(parts[2], 10) || 0;
    } else if (parts.length === 2) {
      // HH:MM format
      hours = parseInt(parts[0], 10) || 0;
      minutes = parseInt(parts[1], 10) || 0;
    } else {
      console.log(`Invalid time format: ${timeStr}`);
      return 0;
    }

  return hours * 3600 + minutes * 60 + seconds;
  } catch (error) {
    console.log(`Error converting time to seconds: ${timeStr}`, error);
    return 0;
  }
}

/**
 * Update bus status in DynamoDB
 * @param {string} busNumber - The bus number
 * @param {string} status - The status ('Active' or 'Inactive')
 */
async function updateBusStatus(busNumber, status) {
  // console.log('busnumber:', busNumber);
  // console.log('status:', status);

  const params = {
    TableName: "Buses-DB",
    Key: {
      busNumber: String(busNumber)
    },
    UpdateExpression: "SET #status = :status",
    ExpressionAttributeNames: {
      "#status": "status"
    },
    ExpressionAttributeValues: {
      ":status": status
    }
  };

  try {
    await dynamoDB.update(params).promise();
    // console.log(`Updated status for bus ${busNumber} to ${status}`);
  } catch (error) {
    console.error("Error updating bus status:", error);
  }
}

/**
 * Clear bus coordinates in DynamoDB
 * @param {string} busNumber - The bus number
 */
async function clearBusCoordinates(busNumber) {
  const params = {
    TableName: "Buses-DB",
    Key: {
      busNumber: String(busNumber)
    },
    UpdateExpression: "REMOVE currentCoordinates"
  };

  try {
    await dynamoDB.update(params).promise();
    console.log(`Cleared coordinates for bus ${busNumber}`);
  } catch (error) {
    console.error("Error clearing bus coordinates:", error);
  }
}

/**
 * Update driver's assigned bus in DynamoDB
 * @param {string} phoneNumber - The driver's phone number
 * @param {string} busNumber - The assigned bus number
 */
async function updateDriverAssignedBus(phoneNumber, busNumber) {
  const params = {
    TableName: "Drivers-DB",
    Key: {
      id: String(phoneNumber)
    },
    UpdateExpression: "SET currentBus = :busNumber",
    ExpressionAttributeValues: {
      ":busNumber": String(busNumber)
    }
  };

  try {
    await dynamoDB.update(params).promise();
    // console.log(`Updated assigned bus for driver ${phoneNumber} to ${busNumber}`);
  } catch (error) {
    console.error("Error updating driver's assigned bus:", error);
  }
}

/**
 * Update driver status in DynamoDB
 * @param {string} phoneNumber - The driver's phone number
 * @param {string} status - The status ('Active' or 'Inactive')
 * @returns {Promise<boolean>} - Success status
 */
async function updateDriverStatus(phoneNumber, status) {
  const params = {
    TableName: "Drivers-DB",
    Key: {
      id: String(phoneNumber)
    },
    UpdateExpression: "SET #status = :status",
    ExpressionAttributeNames: {
      "#status": "status"
    },
    ExpressionAttributeValues: {
      ":status": status
    }
  };

  try {
    await dynamoDB.update(params).promise();
    // console.log(`Updated status for driver ${phoneNumber} to ${status}`);
    return true; // Return success
  } catch (error) {
    console.error("Error updating driver status:", error);
    return false;
  }
}
/**
 * Clear bus history in DynamoDB
 * @param {string} busNumber - The bus number
 */
async function clearBusHistory(busNumber) {
  const params = {
    TableName: "Buses-DB",
    Key: {
      busNumber: String(busNumber)
    },
    UpdateExpression: "SET history = :emptyList",
    ExpressionAttributeValues: {
      ":emptyList": []
    }
  };

  try {
    await dynamoDB.update(params).promise();
    console.log(`Cleared history for bus ${busNumber}`);
  } catch (error) {
    console.error("Error clearing bus history:", error);
  }
}
/**
 * Get bus history from DynamoDB
 * @param {string} busNumber - The bus number
 * @returns {Promise<object|null>} - The bus data or null if not found
 */
async function getBusHistory(busNumber) {
  const params = {
    TableName: "Buses-DB",
    Key: {
      busNumber: String(busNumber)
    },
    ProjectionExpression: "history"
  };

  try {
    const data = await dynamoDB.get(params).promise();
    return data.Item;
  } catch (error) {
    console.error(`Error fetching history for bus ${busNumber}:`, error);
    return null;
  }
}
/**
* Get available (inactive) buses from the master list
 * @param {Array<string>} busList - List of all bus numbers from master data
 * @returns {Promise<Array<string>>} - List of available bus numbers
 */
async function getAvailableBuses(busList) {
  if (!Array.isArray(busList) || busList.length === 0) {
    return [];
  }

  try {
    // Get status of all buses from the master list
    const busStatusPromises = busList.map(async (busNumber) => {
      const params = {
        TableName: "Buses-DB",
        Key: { busNumber: String(busNumber) },
        ProjectionExpression: "#status",
        ExpressionAttributeNames: {
          "#status": "status"
        }
      };

      try {
        const data = await dynamoDB.get(params).promise();
        return {
          busNumber,
          status: data.Item?.status || 'Inactive' // Default to Inactive if status not found
        };
      } catch (error) {
        console.error(`Error fetching status for bus ${busNumber}:`, error);
        return { busNumber, status: 'Unknown' };
      }
    });

    const busStatuses = await Promise.all(busStatusPromises);

    // Filter to only include inactive buses
    const inactiveBuses = busStatuses
      .filter(bus => bus.status === 'Inactive')
      .map(bus => bus.busNumber);

    console.log('Available buses (inactive only):', inactiveBuses);
    return inactiveBuses;
  } catch (error) {
    console.error('Error fetching bus statuses:', error);
    return busList; // Return all buses if there's an error
  }
}

/**
 * Calculate distance between two coordinates using Haversine formula
 * @param {number} lat1 - Latitude of point 1
 * @param {number} lng1 - Longitude of point 1
 * @param {number} lat2 - Latitude of point 2
 * @param {number} lng2 - Longitude of point 2
 * @returns {number} - Distance in kilometers
 */
function calculateDistance(lat1, lng1, lat2, lng2) {
  const R = 6371; // Radius of the earth in km
  const dLat = deg2rad(lat2 - lat1);
  const dLng = deg2rad(lng2 - lng1);
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(deg2rad(lat1)) * Math.cos(deg2rad(lat2)) *
    Math.sin(dLng / 2) * Math.sin(dLng / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  const distance = R * c; // Distance in km
  return distance;
}

function deg2rad(deg) {
  return deg * (Math.PI / 180);
}
/**
 * Start a new journey by clearing history
 * @param {string} busNumber - The bus number
 * @returns {Promise<boolean>} - Success status
 */
async function startNewJourney(busNumber) {
  try {
    // Clear the history for this bus
    await clearBusHistory(busNumber);

    // Update the bus status to Active
    await updateBusStatus(busNumber, 'Active');

    console.log(`Started new journey for bus ${busNumber}`);
    return true;
  } catch (error) {
    console.error(`Error starting new journey for bus ${busNumber}:`, error);
    return false;
  }
}
/**
 * End the current journey
 * @param {string} busNumber - The bus number
 * @param {boolean} keepHistory - Whether to keep the history
 * @returns {Promise<boolean>} - Success status
 */
async function endJourney(busNumber, keepHistory = false) {
  try {
    // If we don't want to keep history, clear it
    if (!keepHistory) {
      await clearBusHistory(busNumber);
    }

    // Update the bus status to Inactive
    await updateBusStatus(busNumber, 'Inactive');

    // Clear current coordinates to indicate the bus is not moving
    await clearBusCoordinates(busNumber);

    // Reset tracking variables - fixed the syntax error by using separate operations
    console.log(`Ended journey for bus ${busNumber} and reset all tracking data`);
    return true;
  } catch (error) {
    console.error(`Error ending journey for bus ${busNumber}:`, error);
    return false;
  }
}
/**
 * Get the current coordinates for a bus
 * @param {string} busNumber - The bus number
 * @returns {Promise<Object|null>} - The current coordinates or null
 */
async function getBusCurrentCoordinates(busNumber) {
  try {
    const params = {
      TableName: "Buses-DB",
      Key: {
        busNumber: String(busNumber)
      },
      ProjectionExpression: "currentCoordinates"
    };

    const data = await dynamoDB.get(params).promise();

    if (data.Item && data.Item.currentCoordinates) {
      // Parse the coordinates string "lat,lng" into an object
      const coordsStr = data.Item.currentCoordinates;
      // Handle both string values and objects with S property (DynamoDB format)
      const actualCoordsStr = typeof coordsStr === 'string' ? coordsStr :
        (coordsStr.S ? coordsStr.S : String(coordsStr));

      const [latitude, longitude] = actualCoordsStr.split(',').map(coord => parseFloat(coord));

      return { latitude, longitude };
    }

    return null;
  } catch (error) {
    console.error('Error getting bus current coordinates:', error);
    return null;
  }
}

/**
 * Save the current coordinates for a bus
 * @param {string} busNumber - The bus number
 * @param {Object} coordinates - The coordinates object with latitude and longitude
 * @returns {Promise<boolean>} - Success status
 */
async function saveBusCurrentCoordinates(busNumber, coordinates) {
  try {
    // Format coordinates as string "lat,lng" to match your DB structure
    const coordsString = `${coordinates.latitude},${coordinates.longitude}`;

    const params = {
      TableName: "Buses-DB",
      Key: {
        busNumber: String(busNumber)
      },
      UpdateExpression: "SET currentCoordinates = :coords",
      ExpressionAttributeValues: {
        ":coords": coordsString
      }
    };

    await dynamoDB.update(params).promise();
    return true;
  } catch (error) {
    console.error('Error saving bus current coordinates:', error);
    return false;
  }
}
/**
 * Update bus delay status in DynamoDB
 * @param {string} busNumber - The bus number
 * @param {boolean} isDelayed - Whether the bus is delayed
 * @returns {Promise<boolean>} - Success status
 */
async function updateBusDelayStatus(busNumber, isDelayed) {
  try {
    const params = {
      TableName: "Buses-DB",
      Key: {
        busNumber: String(busNumber)
      },
      UpdateExpression: "SET isDelayed = :isDelayed",
      ExpressionAttributeValues: {
        ":isDelayed": isDelayed
      }
    };

    await dynamoDB.update(params).promise();
    console.log(`Updated delay status for bus ${busNumber} to ${isDelayed}`);
    return true;
  } catch (error) {
    console.error(`Error updating delay status for bus ${busNumber}:`, error);
    return false;
  }
}

// Update the export to include the new functions
export {
  getBusMasterData,
  updateBusLocation,
  getBusStops,
  updateBusHistory,
  batchUpdateBusHistory,
  updateBusStatus,
  clearBusCoordinates,
  fetchBusStopDetails,
  checkAndUpdateBusStopHistory,
  updateDriverAssignedBus,
  updateDriverStatus,
  clearBusHistory,
  getBusHistory,
  getAvailableBuses,
  startNewJourney,
  endJourney,
  getBusCurrentCoordinates,
  saveBusCurrentCoordinates,
  updateBusDelayStatus
};