import { StyleSheet } from 'react-native';

const createStyles = (theme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.background,
    },
    backgroundImage: {
      flex: 1,
      width: '100%',
    },
    backgroundImageStyle: {
      opacity: 100, // Now we use full opacity since we have separate images
    },
    // Header Card
    headerCard: {
      marginTop: 20,
      marginHorizontal: 20,
      paddingVertical: 20,
      paddingHorizontal: 15,
      borderRadius: 10,
      backgroundColor: theme.secondaryBackground,
      shadowColor: theme.shadow,
      shadowOpacity: 0.1,
      shadowOffset: { width: 0, height: 2 },
      shadowRadius: 5,
      elevation: 3,
      alignItems: 'center',
    },
    headerTopRow: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 10,
    },
    trainIcon: {
      marginRight: 8,
    },
    busNumber: {
      fontSize: 18,
      fontWeight: 'bold',
      color: theme.text,
    },
    separator: {
      height: 1,
      backgroundColor: theme.background,
      marginVertical: 10,
    },
    routeRow: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    routeCircleContainer: {
      alignItems: 'center',
    },
    routeCircle: {
      width: 60,
      height: 60,
      borderRadius: 30,
      backgroundColor: theme.background,
      borderWidth: 1,
      borderColor: theme.borderColor,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 4,
    },
    routeLabel: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.text,
    },
    routeSubtitle: {
      fontSize: 12,
      color: theme.text,
    },
    dottedLine: {
      // Keep any existing positioning/sizing properties from the original style
      flex: 1,
      height: 2,
      borderColor: theme.borderColor,// Blue color matching your primary theme color
      borderStyle: 'dashed',
      borderWidth: 2,
      marginHorizontal: 16,
    },
    themeContainer: {
      flexDirection: 'row',
      justifyContent: 'flex-end',
      marginRight: 30,
      top: 25,
    },
    logoutContainer: {
      flexDirection: 'row',
      justifyContent: 'flex-start',
      marginLeft: 10,
      top: -20,
    },
    headerButtonsContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginHorizontal: 20,
      marginTop: 25,
    },
    // Main Content (Big Start Button)
    mainContent: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
    },
    startButton: {
      width: 200,
      height: 200,
      borderRadius: 100,
      alignItems: 'center',
      justifyContent: 'center',
      shadowColor: theme.shadow,
      shadowOffset: { width: 0, height: 0 },
      shadowOpacity: 0.4,
      shadowRadius: 25,
      elevation: 5,
    },
    startButtonText: {
      fontSize: 24,
      fontWeight: 'bold',
      color: theme.startButtonTextColor,
    },
    // Absolutely position the stop (red X) icon without affecting layout
    stopIconAbsolute: {
      position: 'absolute',
      bottom: -80,
      alignSelf: 'center',
    },
    // Bottom Buttons
    bottomButtonsContainer: {
      flexDirection: 'row',
      marginHorizontal: 20,
      marginBottom: 20,
      justifyContent: 'space-between',
    },
    redirectButton: {
      flex: 1,
      marginRight: 10,
      backgroundColor: theme.redirectButton,
      borderColor: theme.redirectButtonBorderColor,
      borderWidth: 2,
      borderRadius: 10,
      paddingVertical: 15,
      alignItems: 'center',
      shadowColor: theme.shadow,
      shadowOpacity: 0.1,
      shadowOffset: { width: 0, height: 2 },
      shadowRadius: 5,
      elevation: 3,
    },
    redirectButtonText: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.redirectTextColor,
    },
    delayedButton: {
      flex: 1,
      marginLeft: 10,
      backgroundColor: theme.delayedButton,
      borderColor: theme.delayedButtonBorderColor,
      borderWidth: 2,
      borderRadius: 10,
      paddingVertical: 15,
      alignItems: 'center',
      shadowColor: theme.shadow,
      shadowOpacity: 0.1,
      shadowOffset: { width: 0, height: 2 },
      shadowRadius: 5,
      elevation: 3,
    },
    delayedButtonText: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.delayedTextColor,
    },
    // Add this to your existing styles
    updateButton: {
      padding: 8,
      borderRadius: 20,
      backgroundColor: theme.secondaryBackground,
      shadowColor: theme.shadow,
      shadowOpacity: 0.1,
      shadowOffset: { width: 0, height: 2 },
      shadowRadius: 5,
      elevation: 3,
    },
    // Add these styles to your tracking_schema.js file
    testButton: {
      backgroundColor: '#FF9800',
      paddingVertical: 10,
      paddingHorizontal: 15,
      borderRadius: 8,
      marginTop: 20,
      alignSelf: 'center',
    },
    testButtonText: {
      color: 'white',
      fontWeight: 'bold',
      fontSize: 16,
    },// Updated loading styles to properly use theme properties
    loadingOverlay: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: theme.background === '#FFFFFF' ? 'rgba(255, 255, 255, 0.9)' : 'rgba(0, 0, 0, 0.7)',
      justifyContent: 'center',
      alignItems: 'center',
      zIndex: 1000,
    },
    loadingContainer: {
      backgroundColor: theme.background === '#FFFFFF' ? '#FFFFFF' : '#1A1A1A',
      borderRadius: 16,
      padding: 24,
      alignItems: 'center',
      justifyContent: 'center',
      shadowColor: theme.background === '#FFFFFF' ? '#A0A0A0' : '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: theme.background === '#FFFFFF' ? 0.15 : 0.3,
      shadowRadius: 8,
      elevation: 5,
      width: '80%',
      maxWidth: 300,
    },
    loadingIndicator: {
      width: 80,
      height: 80,
      borderRadius: 40,
      backgroundColor: theme.background === '#FFFFFF' ? '#4285F4' : '#2A2A2A',
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 16,
      borderWidth: 1,
      borderColor: theme.background === '#FFFFFF' ? '#3B78E7' : '#333333',
    },
    loadingText: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.background === '#FFFFFF' ? '#333333' : '#FFFFFF',
      marginTop: 8,
      textAlign: 'center',
    },
    loadingContainerInButton: {
      alignItems: 'center',
      justifyContent: 'center',
    },
    stoppingText: {
      color: theme.normal_text, // Changed from theme.colors.white
      fontSize: 18,
      fontWeight: 'bold',
      marginBottom: 10,
    },
    stoppingOverlay: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
      justifyContent: 'center',
      alignItems: 'center',
      zIndex: 1001, // Higher than loadingOverlay to ensure it's on top
    },
    // Print Ticket Button Styles
    printTicketContainer: {
      position: 'absolute',
      bottom: 30,
      left: 20,
      right: 20,
      zIndex: 100,
    },
    printTicketButton: {
      backgroundColor: theme.primary,
      borderRadius: 15,
      paddingVertical: 15,
      paddingHorizontal: 20,
      alignItems: 'center',
      shadowColor: theme.shadow,
      shadowOpacity: 0.2,
      shadowOffset: { width: 0, height: 4 },
      shadowRadius: 8,
      elevation: 5,
    },
    printTicketText: {
      fontSize: 18,
      fontWeight: 'bold',
      color: theme.normal_text,
    },
  });

export default createStyles;
