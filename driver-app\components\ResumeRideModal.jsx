import React from 'react';
import { View, Text, TouchableOpacity, Modal, StyleSheet } from 'react-native';
import { useContext } from 'react';
import { ThemeContext } from '../contexts/ThemeContext';
import { useTranslation } from 'react-i18next';

const ResumeRideModal = ({ visible, onResume, onNewRide, hasCurrentCoordinates, hasHistory }) => {
  const { theme } = useContext(ThemeContext);
  const { t } = useTranslation();

  console.log("hasCurrentCoordinates:", hasCurrentCoordinates);
  console.log("hasHistory:", hasHistory);
  // Determine the scenario:
  // 1. Just started (coordinates but no history)
  // 2. In progress (has history)
  const justStarted = hasCurrentCoordinates && !hasHistory;
  const inProgress = hasHistory;
  
  // Update the button styles to make them more visually appealing
  const styles = StyleSheet.create({
    modalOverlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'center',
      alignItems: 'center',
    },
    modalContent: {
      backgroundColor: theme.background || theme.secondaryBackground || '#FFFFFF',
      borderRadius: 15,
      padding: 20,
      width: '80%',
      alignItems: 'center',
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
    },
    modalTitle: {
      fontSize: 20,
      fontWeight: 'bold',
      marginBottom: 15,
      color: theme.text,
      textAlign: 'center',
    },
    modalText: {
      fontSize: 16,
      marginBottom: 20,
      color: theme.text,
      textAlign: 'center',
    },
    buttonContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      width: '100%',
      marginTop: 10,
    },
    button: {
      paddingVertical: 10,
      paddingHorizontal: 20,
      borderRadius: 8,
      width: '47%', // Slightly reduced to ensure gap between buttons
      alignItems: 'center',
      justifyContent: 'center',
    },
    resumeButton: {
      backgroundColor: '#6C7BFF', // Purple color to match screenshot
    },
    endRideButton: {
      backgroundColor: '#FF5252', // Red color for end ride
    },
    buttonText: {
      color: '#FFFFFF',
      fontWeight: 'bold',
      fontSize: 16,
    },
  });

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={() => {}}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <Text style={styles.modalTitle}>
            {justStarted 
              ? t('tracking.resumeModal.title') 
              : t('tracking.resumeModal.title')}
          </Text>
          
          <Text style={styles.modalText}>
          {justStarted 
              ? t('tracking.resumeModal.message') 
              : t('tracking.resumeModal.message')
            }
          </Text>
          
          <View style={styles.buttonContainer}>
            <TouchableOpacity 
              style={[styles.button, styles.resumeButton]} 
              onPress={onResume}
              activeOpacity={0.8}
            >
              <Text style={styles.buttonText}>
              {justStarted 
                  ? t('tracking.resumeModal.resumeButton') 
                  : t('tracking.resumeModal.resumeButton')}
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[styles.button, styles.endRideButton]} 
              onPress={onNewRide}
              activeOpacity={0.8}
            >
              <Text style={styles.buttonText}>{t('tracking.resumeModal.newRideButton')}</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default ResumeRideModal;