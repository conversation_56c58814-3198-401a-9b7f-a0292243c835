import React, { useContext } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  ScrollView,
  StyleSheet
} from 'react-native';
import { ThemeContext } from '../contexts/ThemeContext';
import FontAwesome6 from '@expo/vector-icons/FontAwesome6';
import { LinearGradient } from 'expo-linear-gradient';

export default function PrintingSetupGuide({ visible, onClose }) {
  const { theme, mode } = useContext(ThemeContext);

  const styles = StyleSheet.create({
    modalOverlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'center',
      alignItems: 'center',
    },
    modalContent: {
      width: '90%',
      maxHeight: '80%',
      backgroundColor: theme.background,
      borderRadius: 15,
      padding: 20,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 20,
    },
    title: {
      fontSize: 18,
      fontWeight: 'bold',
      color: theme.textColor,
    },
    closeButton: {
      padding: 5,
    },
    content: {
      flex: 1,
    },
    section: {
      marginBottom: 20,
    },
    sectionTitle: {
      fontSize: 16,
      fontWeight: 'bold',
      color: theme.textColor,
      marginBottom: 10,
    },
    text: {
      fontSize: 14,
      color: theme.textSecondary,
      lineHeight: 20,
      marginBottom: 8,
    },
    codeBlock: {
      backgroundColor: mode === 'dark' ? '#2A2A2A' : '#F5F5F5',
      padding: 12,
      borderRadius: 8,
      marginVertical: 8,
    },
    code: {
      fontFamily: 'monospace',
      fontSize: 12,
      color: theme.textColor,
    },
    stepContainer: {
      flexDirection: 'row',
      marginBottom: 12,
    },
    stepNumber: {
      width: 24,
      height: 24,
      borderRadius: 12,
      backgroundColor: theme.primary,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: 12,
    },
    stepNumberText: {
      color: 'white',
      fontSize: 12,
      fontWeight: 'bold',
    },
    stepContent: {
      flex: 1,
    },
    stepTitle: {
      fontSize: 14,
      fontWeight: 'bold',
      color: theme.textColor,
      marginBottom: 4,
    },
    stepText: {
      fontSize: 13,
      color: theme.textSecondary,
      lineHeight: 18,
    },
    warningBox: {
      backgroundColor: theme.warning + '20',
      borderColor: theme.warning,
      borderWidth: 1,
      borderRadius: 8,
      padding: 12,
      marginVertical: 10,
    },
    warningText: {
      color: theme.warning,
      fontSize: 13,
      fontWeight: '500',
    },
  });

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          {/* Header */}
          <View style={styles.header}>
            <Text style={styles.title}>Printing Setup Guide</Text>
            <TouchableOpacity style={styles.closeButton} onPress={onClose}>
              <FontAwesome6 name="times" size={20} color={theme.textColor} />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
            {/* Current Status */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Current Status</Text>
              <View style={styles.warningBox}>
                <Text style={styles.warningText}>
                  ⚠️ Some printing dependencies are missing. Follow the steps below to enable full printing functionality.
                </Text>
              </View>
            </View>

            {/* Installation Steps */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Installation Steps</Text>
              
              <View style={styles.stepContainer}>
                <View style={styles.stepNumber}>
                  <Text style={styles.stepNumberText}>1</Text>
                </View>
                <View style={styles.stepContent}>
                  <Text style={styles.stepTitle}>Install Dependencies</Text>
                  <Text style={styles.stepText}>
                    Run the following command in your project directory:
                  </Text>
                  <View style={styles.codeBlock}>
                    <Text style={styles.code}>
                      npm install react-native-ble-plx expo-print expo-file-system expo-media-library expo-sharing
                    </Text>
                  </View>
                </View>
              </View>

              <View style={styles.stepContainer}>
                <View style={styles.stepNumber}>
                  <Text style={styles.stepNumberText}>2</Text>
                </View>
                <View style={styles.stepContent}>
                  <Text style={styles.stepTitle}>Rebuild the App</Text>
                  <Text style={styles.stepText}>
                    Since this is an Expo development build, you'll need to rebuild:
                  </Text>
                  <View style={styles.codeBlock}>
                    <Text style={styles.code}>
                      npx expo run:android{'\n'}# or{'\n'}npx expo run:ios
                    </Text>
                  </View>
                </View>
              </View>

              <View style={styles.stepContainer}>
                <View style={styles.stepNumber}>
                  <Text style={styles.stepNumberText}>3</Text>
                </View>
                <View style={styles.stepContent}>
                  <Text style={styles.stepTitle}>Configure Permissions</Text>
                  <Text style={styles.stepText}>
                    Add Bluetooth permissions to your app.json or app.config.js:
                  </Text>
                  <View style={styles.codeBlock}>
                    <Text style={styles.code}>
                      "permissions": [{'\n'}  "android.permission.BLUETOOTH",{'\n'}  "android.permission.BLUETOOTH_ADMIN",{'\n'}  "android.permission.ACCESS_FINE_LOCATION"{'\n'}]
                    </Text>
                  </View>
                </View>
              </View>
            </View>

            {/* Alternative Options */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Alternative Printing Options</Text>
              <Text style={styles.text}>
                While setting up Bluetooth printing, you can still use:
              </Text>
              <Text style={styles.text}>• PDF generation and sharing</Text>
              <Text style={styles.text}>• External printing apps (like RawBT)</Text>
              <Text style={styles.text}>• System print dialog</Text>
            </View>

            {/* Troubleshooting */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Troubleshooting</Text>
              <Text style={styles.text}>
                If you encounter issues:
              </Text>
              <Text style={styles.text}>
                1. Ensure your device supports Bluetooth Low Energy (BLE)
              </Text>
              <Text style={styles.text}>
                2. Grant location permissions (required for BLE scanning)
              </Text>
              <Text style={styles.text}>
                3. Make sure your thermal printer supports ESC/POS commands
              </Text>
              <Text style={styles.text}>
                4. Try restarting the app after installation
              </Text>
            </View>
          </ScrollView>
        </View>
      </View>
    </Modal>
  );
}
