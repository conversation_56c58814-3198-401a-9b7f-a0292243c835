import { Alert, Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Safely import BleManager with fallback
let BleManager;
try {
  const bleModule = require('react-native-ble-plx');
  BleManager = bleModule.BleManager;
} catch (error) {
  console.warn('react-native-ble-plx not available:', error.message);
  BleManager = null;
}

class BluetoothPrinterService {
  constructor() {
    this.manager = null;
    this.connectedDevice = null;
    this.isScanning = false;
    this.printerCharacteristic = null;
    this.isAvailable = false;

    // Initialize BLE manager if available
    if (BleManager) {
      try {
        this.manager = new BleManager();
        this.isAvailable = true;
      } catch (error) {
        console.warn('Failed to initialize BleManager:', error.message);
        this.isAvailable = false;
      }
    }
  }

  // Initialize the Bluetooth manager
  async initialize() {
    if (!this.isAvailable || !this.manager) {
      console.warn('Bluetooth not available on this device');
      return false;
    }

    try {
      const state = await this.manager.state();
      if (state === 'PoweredOff') {
        Alert.alert('Bluetooth', 'Please turn on Bluetooth to use printer functionality');
        return false;
      }
      return true;
    } catch (error) {
      console.error('Bluetooth initialization error:', error);
      return false;
    }
  }

  // Scan for available Bluetooth devices
  async scanForPrinters(onDeviceFound) {
    if (!this.isAvailable || !this.manager) {
      console.warn('Bluetooth not available for scanning');
      return;
    }

    if (this.isScanning) return;

    this.isScanning = true;
    const foundDevices = new Set();

    try {
      this.manager.startDeviceScan(null, null, (error, device) => {
        if (error) {
          console.error('Scan error:', error);
          this.stopScan();
          return;
        }

        if (device && device.name && !foundDevices.has(device.id)) {
          // Filter for potential thermal printers
          const deviceName = device.name.toLowerCase();
          if (deviceName.includes('printer') ||
              deviceName.includes('pos') ||
              deviceName.includes('thermal') ||
              deviceName.includes('receipt') ||
              deviceName.includes('bt') ||
              device.name.length > 0) {
            foundDevices.add(device.id);
            onDeviceFound(device);
          }
        }
      });

      // Stop scanning after 10 seconds
      setTimeout(() => {
        this.stopScan();
      }, 10000);

    } catch (error) {
      console.error('Scan start error:', error);
      this.isScanning = false;
    }
  }

  // Stop scanning for devices
  stopScan() {
    if (this.isScanning && this.manager) {
      this.manager.stopDeviceScan();
      this.isScanning = false;
    }
  }

  // Connect to a specific printer device
  async connectToPrinter(deviceId) {
    if (!this.isAvailable || !this.manager) {
      throw new Error('Bluetooth not available on this device');
    }

    try {
      if (this.connectedDevice) {
        await this.disconnect();
      }

      const device = await this.manager.connectToDevice(deviceId);
      await device.discoverAllServicesAndCharacteristics();

      // Find the appropriate service and characteristic for printing
      const services = await device.services();
      let printCharacteristic = null;

      for (const service of services) {
        const characteristics = await service.characteristics();
        for (const char of characteristics) {
          // Look for writable characteristics (common UUIDs for thermal printers)
          if (char.isWritableWithoutResponse || char.isWritableWithResponse) {
            printCharacteristic = char;
            break;
          }
        }
        if (printCharacteristic) break;
      }

      if (!printCharacteristic) {
        throw new Error('No writable characteristic found for printing');
      }

      this.connectedDevice = device;
      this.printerCharacteristic = printCharacteristic;

      // Save connected device for auto-reconnect
      await AsyncStorage.setItem('lastConnectedPrinter', JSON.stringify({
        id: device.id,
        name: device.name
      }));

      return true;
    } catch (error) {
      console.error('Connection error:', error);
      throw error;
    }
  }

  // Disconnect from current printer
  async disconnect() {
    try {
      if (this.connectedDevice) {
        await this.connectedDevice.cancelConnection();
        this.connectedDevice = null;
        this.printerCharacteristic = null;
      }
    } catch (error) {
      console.error('Disconnect error:', error);
    }
  }

  // Check if printer is connected
  isConnected() {
    return this.connectedDevice !== null && this.printerCharacteristic !== null;
  }

  // Get connected device info
  getConnectedDeviceInfo() {
    if (this.connectedDevice) {
      return {
        id: this.connectedDevice.id,
        name: this.connectedDevice.name
      };
    }
    return null;
  }

  // Try to reconnect to last connected printer
  async autoReconnect() {
    try {
      const lastPrinter = await AsyncStorage.getItem('lastConnectedPrinter');
      if (lastPrinter) {
        const printerInfo = JSON.parse(lastPrinter);
        await this.connectToPrinter(printerInfo.id);
        return true;
      }
    } catch (error) {
      console.error('Auto-reconnect failed:', error);
    }
    return false;
  }

  // ESC/POS Commands
  getESCPOSCommands() {
    return {
      INIT: '\x1B\x40',           // Initialize printer
      FEED_LINE: '\x0A',          // Line feed
      CUT_PAPER: '\x1D\x56\x00',  // Cut paper
      ALIGN_LEFT: '\x1B\x61\x00', // Align left
      ALIGN_CENTER: '\x1B\x61\x01', // Align center
      ALIGN_RIGHT: '\x1B\x61\x02', // Align right
      BOLD_ON: '\x1B\x45\x01',    // Bold on
      BOLD_OFF: '\x1B\x45\x00',   // Bold off
      UNDERLINE_ON: '\x1B\x2D\x01', // Underline on
      UNDERLINE_OFF: '\x1B\x2D\x00', // Underline off
      DOUBLE_HEIGHT: '\x1B\x21\x10', // Double height
      DOUBLE_WIDTH: '\x1B\x21\x20',  // Double width
      NORMAL_SIZE: '\x1B\x21\x00',   // Normal size
      SMALL_FONT: '\x1B\x4D\x01',    // Small font
      NORMAL_FONT: '\x1B\x4D\x00',   // Normal font
    };
  }

  // Convert string to bytes for printing
  stringToBytes(str) {
    const bytes = [];
    for (let i = 0; i < str.length; i++) {
      bytes.push(str.charCodeAt(i));
    }
    return new Uint8Array(bytes);
  }

  // Send data to printer
  async sendToPrinter(data) {
    if (!this.isConnected()) {
      throw new Error('Printer not connected');
    }

    try {
      const bytes = typeof data === 'string' ? this.stringToBytes(data) : data;
      const base64Data = btoa(String.fromCharCode.apply(null, bytes));

      await this.printerCharacteristic.writeWithoutResponse(base64Data);
      return true;
    } catch (error) {
      console.error('Print error:', error);
      throw error;
    }
  }

  // Print ticket with formatted data
  async printTicket(ticketData) {
    if (!this.isConnected()) {
      throw new Error('Printer not connected. Please connect to a printer first.');
    }

    try {
      const commands = this.getESCPOSCommands();
      let printData = '';

      // Initialize printer
      printData += commands.INIT;

      // Header
      printData += commands.ALIGN_CENTER;
      printData += commands.BOLD_ON;
      printData += commands.DOUBLE_HEIGHT;
      printData += 'BUS TRANSPORT\n';
      printData += commands.NORMAL_SIZE;
      printData += commands.BOLD_OFF;

      // Separator line
      printData += commands.ALIGN_CENTER;
      printData += '--------------------------------\n';

      // Ticket number and date
      printData += commands.ALIGN_LEFT;
      printData += commands.NORMAL_FONT;
      printData += `Ticket: ${ticketData.ticketNumber}\n`;
      printData += `Date: ${ticketData.date}\n`;
      printData += `Time: ${ticketData.time}\n`;

      // Route information
      printData += commands.ALIGN_CENTER;
      printData += commands.BOLD_ON;
      printData += `${ticketData.fromCity} TO ${ticketData.toCity}\n`;
      printData += commands.BOLD_OFF;

      // Passenger and fare details
      printData += commands.ALIGN_LEFT;
      printData += `Passenger: ${ticketData.passengerType}\n`;
      printData += `Quantity: 1\n`;
      printData += `Fare: Rs ${ticketData.fareAmount}\n`;
      printData += `Bus: ${ticketData.busNumber || 'N/A'}\n`;

      // Footer separator
      printData += commands.ALIGN_CENTER;
      printData += '--------------------------------\n';
      printData += 'Have a nice journey!\n';

      // Feed lines and cut
      printData += '\n\n\n';
      printData += commands.CUT_PAPER;

      await this.sendToPrinter(printData);
      return true;
    } catch (error) {
      console.error('Ticket print error:', error);
      throw error;
    }
  }

  // Cleanup when service is destroyed
  destroy() {
    this.stopScan();
    this.disconnect();
    if (this.manager) {
      this.manager.destroy();
    }
  }
}

export default new BluetoothPrinterService();
