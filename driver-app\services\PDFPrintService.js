import { Alert } from 'react-native';

// Safely import Expo modules with fallbacks
let Print, Sharing, FileSystem, MediaLibrary;
let isAvailable = false;

try {
  Print = require('expo-print');
  Sharing = require('expo-sharing');
  FileSystem = require('expo-file-system');
  MediaLibrary = require('expo-media-library');
  isAvailable = true;
} catch (error) {
  console.warn('Some Expo modules not available:', error.message);
  isAvailable = false;
}

class PDFPrintService {
  // Generate HTML template for ticket
  generateTicketHTML(ticketData) {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
          body {
            font-family: 'Courier New', monospace;
            margin: 0;
            padding: 20px;
            background: white;
            font-size: 14px;
            line-height: 1.4;
          }
          .ticket {
            max-width: 300px;
            margin: 0 auto;
            border: 2px dashed #333;
            padding: 20px;
            background: white;
          }
          .header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 1px dashed #333;
            padding-bottom: 15px;
          }
          .company-name {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
            letter-spacing: 1px;
          }
          .ticket-info {
            margin-bottom: 15px;
          }
          .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 2px 0;
          }
          .label {
            font-weight: bold;
          }
          .value {
            text-align: right;
          }
          .route {
            text-align: center;
            font-size: 16px;
            font-weight: bold;
            margin: 15px 0;
            padding: 10px;
            border: 1px solid #333;
            background: #f5f5f5;
          }
          .fare-section {
            border-top: 1px dashed #333;
            border-bottom: 1px dashed #333;
            padding: 10px 0;
            margin: 15px 0;
          }
          .fare-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
          }
          .total-fare {
            font-size: 16px;
            font-weight: bold;
            border-top: 1px solid #333;
            padding-top: 8px;
            margin-top: 8px;
          }
          .footer {
            text-align: center;
            margin-top: 20px;
            border-top: 1px dashed #333;
            padding-top: 15px;
            font-size: 12px;
          }
          .separator {
            text-align: center;
            margin: 10px 0;
            font-size: 12px;
          }
          @media print {
            body { margin: 0; padding: 10px; }
            .ticket { max-width: none; border: none; }
          }
        </style>
      </head>
      <body>
        <div class="ticket">
          <div class="header">
            <div class="company-name">BUS TRANSPORT</div>
            <div>TICKET RECEIPT</div>
          </div>

          <div class="separator">
            --------------------------------
          </div>

          <div class="ticket-info">
            <div class="info-row">
              <span class="label">Ticket No:</span>
              <span class="value">${ticketData.ticketNumber}</span>
            </div>
            <div class="info-row">
              <span class="label">Date:</span>
              <span class="value">${ticketData.date}</span>
            </div>
            <div class="info-row">
              <span class="label">Time:</span>
              <span class="value">${ticketData.time}</span>
            </div>
            <div class="info-row">
              <span class="label">Bus No:</span>
              <span class="value">${ticketData.busNumber}</span>
            </div>
          </div>

          <div class="route">
            ${ticketData.fromCity} TO ${ticketData.toCity}
          </div>

          <div class="fare-section">
            <div class="fare-row">
              <span>Passenger Type:</span>
              <span>${ticketData.passengerType}</span>
            </div>
            <div class="fare-row">
              <span>Quantity:</span>
              <span>1</span>
            </div>
            <div class="fare-row total-fare">
              <span>Total Fare:</span>
              <span>Rs ${ticketData.fareAmount}</span>
            </div>
          </div>

          <div class="separator">
            --------------------------------
          </div>

          <div class="footer">
            <div>Have a nice journey!</div>
            <div style="margin-top: 10px; font-size: 10px;">
              Thank you for choosing our service
            </div>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  // Print ticket using expo-print
  async printTicket(ticketData) {
    if (!isAvailable) {
      throw new Error('PDF printing not available. Please install required dependencies.');
    }

    try {
      const html = this.generateTicketHTML(ticketData);

      // Create PDF
      const { uri } = await Print.printToFileAsync({
        html,
        base64: false,
        width: 300,
        height: 600,
      });

      // Check if sharing is available
      const isAvailable = await Sharing.isAvailableAsync();

      if (isAvailable) {
        // Share the PDF
        await Sharing.shareAsync(uri, {
          mimeType: 'application/pdf',
          dialogTitle: 'Print or Save Ticket',
          UTI: 'com.adobe.pdf'
        });
        return true;
      } else {
        // Fallback: Save to device storage
        return await this.saveToDevice(uri, ticketData.ticketNumber);
      }
    } catch (error) {
      console.error('PDF Print error:', error);
      throw error;
    }
  }

  // Save PDF to device storage
  async saveToDevice(uri, ticketNumber) {
    try {
      // Request media library permissions
      const { status } = await MediaLibrary.requestPermissionsAsync();

      if (status !== 'granted') {
        Alert.alert(
          'Permission Required',
          'Please grant media library permission to save tickets.',
          [{ text: 'OK' }]
        );
        return false;
      }

      // Create asset from the PDF file
      const asset = await MediaLibrary.createAssetAsync(uri);

      // Create or get the tickets album
      let album = await MediaLibrary.getAlbumAsync('Bus Tickets');
      if (!album) {
        album = await MediaLibrary.createAlbumAsync('Bus Tickets', asset, false);
      } else {
        await MediaLibrary.addAssetsToAlbumAsync([asset], album, false);
      }

      Alert.alert(
        'Ticket Saved',
        `Ticket ${ticketNumber} has been saved to your device in the "Bus Tickets" album.`,
        [{ text: 'OK' }]
      );

      return true;
    } catch (error) {
      console.error('Save to device error:', error);
      throw new Error('Failed to save ticket to device');
    }
  }

  // Print using system print dialog
  async printDirectly(ticketData) {
    try {
      const html = this.generateTicketHTML(ticketData);

      await Print.printAsync({
        html,
        printerUrl: undefined, // Let user select printer
      });

      return true;
    } catch (error) {
      console.error('Direct print error:', error);
      throw error;
    }
  }

  // Generate ticket for external printing apps (like RawBT)
  async generateForExternalPrint(ticketData) {
    try {
      // Create a simple text format for thermal printers
      const textContent = this.generateTextTicket(ticketData);

      // Save as text file
      const fileName = `ticket_${ticketData.ticketNumber}.txt`;
      const fileUri = FileSystem.documentDirectory + fileName;

      await FileSystem.writeAsStringAsync(fileUri, textContent);

      // Save to auto print folder for RawBT
      const { status } = await MediaLibrary.requestPermissionsAsync();
      if (status === 'granted') {
        const asset = await MediaLibrary.createAssetAsync(fileUri);

        let album = await MediaLibrary.getAlbumAsync('auto print');
        if (!album) {
          album = await MediaLibrary.createAlbumAsync('auto print', asset, false);
        } else {
          await MediaLibrary.addAssetsToAlbumAsync([asset], album, false);
        }

        Alert.alert(
          'Ready for External Printing',
          'Ticket file has been saved to "auto print" folder for external printing apps.',
          [{ text: 'OK' }]
        );
      }

      return true;
    } catch (error) {
      console.error('External print generation error:', error);
      throw error;
    }
  }

  // Generate simple text format for thermal printers
  generateTextTicket(ticketData) {
    return `
        BUS TRANSPORT

--------------------------------

Ticket: ${ticketData.ticketNumber}
Date: ${ticketData.date}
Time: ${ticketData.time}
Bus: ${ticketData.busNumber}

${ticketData.fromCity} TO ${ticketData.toCity}

Passenger: ${ticketData.passengerType}
Quantity: 1
Fare: Rs ${ticketData.fareAmount}

--------------------------------

Have a nice journey!

    `;
  }
}

export default new PDFPrintService();
