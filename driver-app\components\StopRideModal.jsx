import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, Modal, StyleSheet } from 'react-native';
import { useContext } from 'react';
import { ThemeContext } from '../contexts/ThemeContext';
import { useTranslation } from 'react-i18next';
// Remove BusNumberConfirmationModal import as we're not using it anymore

const StopRideModal = ({ visible, onCancel, onConfirm, busNumber }) => {
  const { theme, mode } = useContext(ThemeContext);
  const { t } = useTranslation();
  // Remove confirmModalVisible state as we're not showing the confirmation modal anymore
  const [countdown, setCountdown] = useState(10);

  // Reset countdown when modal becomes visible
  useEffect(() => {
    if (visible) {
      setCountdown(10);
    }
  }, [visible]);

  useEffect(() => {
    let timer;
    if (visible && countdown > 0) {
      timer = setInterval(() => {
        setCountdown(prev => prev - 1);
      }, 1000);
    } else if (countdown === 0) {
      clearInterval(timer);
    }
    return () => clearInterval(timer);
  }, [visible, countdown]);

  // No need for handleStopRequest anymore as we're not showing a second modal

  // The styles remain the same
  const styles = StyleSheet.create({
    modalOverlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'center',
      alignItems: 'center',
    },
    modalContent: {
        backgroundColor: theme.secondaryBackground || '#FFFFFF',
      borderRadius: 15,
      padding: 20,
      width: '80%',
      alignItems: 'center',
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
      opacity: 1 
    },
    modalTitle: {
      fontSize: 20,
      fontWeight: 'bold',
      color: theme.text,
      marginBottom: 15,
    },
    modalText: {
      fontSize: 16,
      color: theme.text,
      textAlign: 'center',
      marginBottom: 20,
    },
    countdownNumber: {
      fontSize: 28,
      fontWeight: 'bold',
      color: theme.text,
    },
    modalButtons: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      width: '100%',
    },
    modalButton: {
      paddingVertical: 10,
      paddingHorizontal: 20,
      borderRadius: 8,
      minWidth: '40%',
      alignItems: 'center',
    },
    cancelButton: {
      backgroundColor: theme.cancelButton || '#ccc',
    },
    confirmButton: {
      backgroundColor: countdown > 0 ? (theme.cancelButton || '#ccc') : (theme.danger || '#ff3b30'),
    },
    modalButtonText: {
      color: '#fff',
      fontWeight: 'bold',
      fontSize: 16,
    },
  });

  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={visible}
      onRequestClose={onCancel}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <Text style={styles.modalTitle}>{t('tracking.stopModal.title')}</Text>
          <Text style={styles.modalText}>
            {t('tracking.stopModal.message')}
          </Text>
          <Text style={styles.modalText}>
            {countdown > 0 ? (
              <>
                {t('tracking.stopModal.waitMessage', { seconds: '' })}
                <Text style={styles.countdownNumber}> {countdown} </Text>
              </>
            ) : (
              t('tracking.stopModal.readyMessage')
            )}
          </Text>
          <View style={styles.modalButtons}>
            <TouchableOpacity
              style={[styles.modalButton, styles.cancelButton]}
              onPress={onCancel}
            >
              <Text style={styles.modalButtonText}>{t('common.cancel')}</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.modalButton, styles.confirmButton]}
              onPress={onConfirm}
              disabled={countdown > 0}
            >
              <Text style={styles.modalButtonText}>{t('tracking.stopModal.confirmButton')}</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default StopRideModal;