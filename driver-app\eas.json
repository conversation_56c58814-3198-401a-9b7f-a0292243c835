{"cli": {"version": ">= 5.9.1", "appVersionSource": "remote"}, "build": {"development": {"developmentClient": true, "distribution": "internal", "channel": "development", "android": {"gradleCommand": ":app:assembleDebug"}}, "testing": {"developmentClient": true, "distribution": "internal", "channel": "testing", "android": {"gradleCommand": ":app:assembleDebug"}}, "preview": {"distribution": "internal", "android": {"buildType": "apk", "gradleCommand": ":app:assembleRelease"}, "channel": "preview"}, "nettest": {"distribution": "internal", "android": {"buildType": "apk", "gradleCommand": ":app:assembleRelease"}, "channel": "nettest"}, "production": {"android": {"buildType": "apk", "gradleCommand": ":app:assembleRelease"}, "channel": "production"}}, "submit": {"production": {}}}