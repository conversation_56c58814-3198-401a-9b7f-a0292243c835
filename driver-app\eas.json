{"cli": {"version": ">= 5.9.1", "appVersionSource": "remote"}, "build": {"development": {"developmentClient": true, "distribution": "internal", "channel": "development", "android": {"gradleCommand": ":app:assembleDebug", "buildType": "apk"}, "env": {"EXPO_NO_CAPABILITY_SYNC": "1"}}, "testing": {"developmentClient": true, "distribution": "internal", "channel": "testing", "android": {"gradleCommand": ":app:assembleDebug", "buildType": "apk"}, "env": {"EXPO_NO_CAPABILITY_SYNC": "1"}}, "preview": {"distribution": "internal", "android": {"buildType": "apk", "gradleCommand": ":app:assembleRelease"}, "channel": "preview", "env": {"EXPO_NO_CAPABILITY_SYNC": "1"}}, "nettest": {"distribution": "internal", "android": {"buildType": "apk", "gradleCommand": ":app:assembleRelease"}, "channel": "nettest", "env": {"EXPO_NO_CAPABILITY_SYNC": "1"}}, "production": {"android": {"buildType": "apk", "gradleCommand": ":app:assembleRelease"}, "channel": "production", "env": {"EXPO_NO_CAPABILITY_SYNC": "1"}}}, "submit": {"production": {}}}