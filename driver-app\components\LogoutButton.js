import React, { useState, useContext } from 'react';
import { TouchableOpacity, View, Text, Modal, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useNavigation } from '@react-navigation/native';
import { ThemeContext } from '../contexts/ThemeContext';
import { stopTracking } from '../scripts/LocationTracker';
import { updateDriverAssignedBus, updateBusStatus, updateDriverStatus } from '../scripts/DynamoData';
import { useTranslation } from 'react-i18next';
import useBusStore from '../stores/busStore'; 

const LogoutButton = () => {
  const { theme } = useContext(ThemeContext);
  const navigation = useNavigation();
  const [modalVisible, setModalVisible] = useState(false);
  const { t } = useTranslation();
  const clearBusStore = useBusStore(state => state.clearStore);

  const handleLogout = async () => {
    try {
      await stopTracking();
       // Get stored values before removing them
       const phoneNumber = await AsyncStorage.getItem('phoneNumber');
       const busNumber = await AsyncStorage.getItem('busNumber');
       console.log('Bus number:', busNumber);
       console.log('Phone number:', phoneNumber);
       
       // Update driver's assigned bus to "unassigned" in DynamoDB
       if (phoneNumber) {
         await updateDriverAssignedBus(phoneNumber, "Unassigned");
         await updateDriverStatus(phoneNumber, "Inactive");
       }
       
       // Update bus status to "Inactive" in DynamoDB
       if (busNumber) {
         await updateBusStatus(busNumber, "Inactive");
         // Clear the bus store data
         await clearBusStore(busNumber);
         console.log(`Cleared bus store data for bus ${busNumber}`);
       }
       
       // Clear local storage
       await AsyncStorage.removeItem('isLoggedIn');
       await AsyncStorage.removeItem('busNumber');
       await AsyncStorage.removeItem('phoneNumber');
       
      setModalVisible(false);
      navigation.reset({
        index: 0,
        routes: [{ name: 'UID' }],
      });
    } catch (error) {
      console.error('Error logging out:', error);
    }
  };

  return (
    <>
      <TouchableOpacity 
        onPress={() => setModalVisible(true)} 
        style={{ borderRadius: 10, overflow: 'hidden' }}
      >
        <View
          style={{
            backgroundColor: theme.secondaryBackground,
            borderRadius: 10,
            padding: 10,
            borderWidth: 1,
            borderColor: theme.borderColor,
            alignItems: 'center',
            justifyContent: 'center',
            width: 50,
            height: 50,
          }}
        >
          <Ionicons name="log-out-outline" size={24} color={theme.text}  />
        </View>
      </TouchableOpacity>

      <Modal
        visible={modalVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={modalStyles.modalBackground}>
          <View style={[modalStyles.modalContainer, { backgroundColor: theme.background }]}>
            <Text style={[modalStyles.modalTitle, { color: theme.text }]}>
            {t('common.logout')}
            </Text>
            <Text style={[modalStyles.modalMessage, { color: theme.text }]}>
            {t('tracking.confirmStop')}
            </Text>
            <View style={modalStyles.modalButtonsContainer}>
              <TouchableOpacity 
                onPress={() => setModalVisible(false)} 
                style={[
                  modalStyles.modalButton,
                  { borderColor: theme.borderColor }
                ]}
              >
                <Text style={[modalStyles.modalButtonText, { color: theme.text }]}>{t('common.cancel')}</Text>
              </TouchableOpacity>
              <TouchableOpacity 
                onPress={handleLogout} 
                style={[
                  modalStyles.modalButton,
                  { borderColor: theme.borderColor }
                ]}
              >
                <Text style={[modalStyles.modalButtonText, { color: theme.text }]}>{t('common.logout')}</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </>
  );
};

const modalStyles = StyleSheet.create({
  modalBackground: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)', // semi-transparent overlay
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: '80%',
    borderRadius: 10,
    padding: 20,
    alignItems: 'center',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  modalMessage: {
    fontSize: 16,
    marginBottom: 20,
    textAlign: 'center',
  },
  modalButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  modalButton: {
    flex: 1,
    marginHorizontal: 10,
    paddingVertical: 10,
    alignItems: 'center',
    borderRadius: 5,
    borderWidth: 1,
  },
  modalButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default LogoutButton;
