import React, { useState, useEffect } from 'react';
import { View, Text, Modal, TouchableOpacity, StyleSheet, ScrollView } from 'react-native';
import { FontAwesome, MaterialIcons } from '@expo/vector-icons';
import * as Updates from 'expo-updates';
import { EventRegister } from 'react-native-event-listeners';
import { fetchAndApplyUpdate, checkForUpdates as checkForUpdatesUtil, getCurrentVersion } from '../utils/updateUtils';

const UpdateModal = () => {
  const [isUpdating, setIsUpdating] = useState(false);
  const [updateProgress, setUpdateProgress] = useState(0);
  const [updateAvailable, setUpdateAvailable] = useState(false);
  const [noUpdates, setNoUpdates] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [currentVersion, setCurrentVersion] = useState('');
  const [releaseNotes, setReleaseNotes] = useState('');
  const [updateDownloaded, setUpdateDownloaded] = useState(false);
  
  // In the useEffect, add a listener for the new updateDownloaded event
  useEffect(() => {
    // Add console logs to help debug
    console.log("UpdateModal mounted, checking for updates...");
    // For initial check, don't show "no updates" alert
    initialCheckForUpdates();
    setCurrentVersion(getCurrentVersion());
    
    // Set up event listeners
    const updateAvailableListener = EventRegister.addEventListener(
      'updateAvailable',
      (data) => {
        console.log("Update available event received:", data);
        setUpdateAvailable(data.available);
        setNoUpdates(false);
        setErrorMessage('');
        if (data.notes) {
          setReleaseNotes(data.notes);
        }
      }
    );
    
    // Add new listener for update downloaded event
    const updateDownloadedListener = EventRegister.addEventListener(
      'updateDownloaded',
      (success) => {
        console.log("Update downloaded event received:", success);
        setIsUpdating(false);
        setUpdateDownloaded(success);
      }
    );
    
    const noUpdatesListener = EventRegister.addEventListener(
      'noUpdatesAvailable',
      (data) => {
        console.log("No updates event received with data:", JSON.stringify(data));
        setNoUpdates(true);
        setUpdateAvailable(false);
        setErrorMessage('');
        if (data && data.version) {
          setCurrentVersion(data.version);
        }
        if (data && data.notes) {
          console.log("Setting release notes:", data.notes);
          setReleaseNotes(data.notes);
        } else {
          console.log("No release notes in data");
        }
      }
    );
    
    const updateErrorListener = EventRegister.addEventListener(
      'updateError',
      (message) => {
        setErrorMessage(message);
        setUpdateAvailable(false);
        setNoUpdates(false);
      }
    );
    
    const updateDownloadingListener = EventRegister.addEventListener(
      'updateDownloading',
      (downloading) => {
        setIsUpdating(downloading);
        if (downloading) {
          simulateProgress();
        }
      }
    );
    
    // Remove the manual check listener
    
    // Clean up listeners on unmount
    return () => {
      EventRegister.removeEventListener(updateAvailableListener);
      EventRegister.removeEventListener(noUpdatesListener);
      EventRegister.removeEventListener(updateErrorListener);
      EventRegister.removeEventListener(updateDownloadingListener);
      EventRegister.removeEventListener(updateDownloadedListener); // Add this
    };
  }, []);

  // Add a separate function for initial check
  const initialCheckForUpdates = async () => {
    try {
      console.log("Initial check for updates...");
      // For initial check, don't show "no updates" alert (false)
      await checkForUpdatesUtil(false);
      console.log("Initial update check completed");
    } catch (error) {
      console.log('Error in initial update check:', error);
      // Don't show error for initial check
    }
  };

  // Remove the manual check function

  const simulateProgress = () => {
    setUpdateProgress(0);
    let progress = 0;
    const progressInterval = setInterval(() => {
      progress += 5;
      if (progress > 95) {
        clearInterval(progressInterval);
      }
      setUpdateProgress(progress);
    }, 300);
    
    // Clear interval after 6 seconds (when update should be complete)
    setTimeout(() => {
      clearInterval(progressInterval);
      setUpdateProgress(100);
    }, 6000);
  };

  const dismissModal = () => {
    // Only dismiss for no updates or errors, not for available updates
    if (!updateAvailable) {
      setNoUpdates(false);
      setErrorMessage('');
    }
  };

  // Change this line
  // Modify the condition to include updateDownloaded
  if (!updateAvailable && !isUpdating && !noUpdates && !errorMessage && !updateDownloaded) {
    // Return an empty fragment instead of null to keep the component mounted
    return <></>;
  }
  
  // Add a new function to handle restart
  const handleRestart = () => {
    console.log("Restarting app to apply update...");
    Updates.reloadAsync();
  };
  
  // Add a new UI for when update is downloaded
  if (updateDownloaded) {
    return (
      <Modal transparent={true} animationType="fade" visible={true}>
        <View style={styles.modalOverlay}>
          <View style={styles.updateModal}>
            <MaterialIcons name="check-circle" size={40} color="#4CAF50" style={styles.modalIcon} />
            <Text style={styles.modalTitle}>Update Ready</Text>
            <Text style={styles.modalText}>
              The update has been downloaded successfully. Please restart the app to apply the changes.
            </Text>
            
            {/* {releaseNotes && (
              <View style={styles.releaseNotesContainer}>
                <Text style={styles.releaseNotesTitle}>What's new in this update:</Text>
                <ScrollView style={styles.releaseNotesScroll}>
                  <Text style={styles.releaseNotesText}>{releaseNotes}</Text>
                </ScrollView>
              </View>
            )} */}
            
            <View style={styles.buttonContainer}>
              <TouchableOpacity 
                style={[styles.updateButton, {width: '100%', backgroundColor: '#4CAF50'}]} 
                onPress={handleRestart}
              >
                <Text style={styles.updateButtonText}>Restart Now</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    );
  }
  if (isUpdating) {
    return (
      <Modal transparent={true} animationType="fade" visible={true}>
        <View style={styles.modalOverlay}>
          <View style={styles.updateCard}>
            <FontAwesome name="refresh" size={50} color="#6777E8" style={styles.updateIcon} />
            <Text style={styles.updateTitle}>Updating Application</Text>
            <Text style={styles.updateSubtitle}>Please wait while we download the latest version</Text>
            
            <View style={styles.progressBarContainer}>
              <View style={[styles.progressBar, { width: `${updateProgress}%` }]} />
            </View>
            <Text style={styles.progressText}>{updateProgress}%</Text>
            
            <Text style={styles.updateNote}>The app will restart automatically when the update is complete</Text>
          </View>
        </View>
      </Modal>
    );
  }

  // In the noUpdates modal section
  if (noUpdates) {
    console.log("Rendering noUpdates modal with releaseNotes:", releaseNotes);
    return (
      <Modal
        transparent={true}
        animationType="fade"
        visible={true}
        onRequestClose={dismissModal}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.updateModal}>
            <MaterialIcons name="check-circle" size={40} color="#4CAF50" style={styles.modalIcon} />
            <Text style={styles.modalTitle}>You're Up to Date</Text>
            <Text style={styles.modalText}>
              You are using the latest version {currentVersion ? `(${currentVersion})` : ''} of the application.
            </Text>
            
            {releaseNotes && releaseNotes.length > 0 ? (
              <View style={styles.releaseNotesContainer}>
                <Text style={styles.releaseNotesTitle}>What's in this version:</Text>
                <ScrollView style={styles.releaseNotesScroll}>
                  <Text style={styles.releaseNotesText}>{releaseNotes}</Text>
                </ScrollView>
              </View>
            ) : null}
            
            <View style={styles.buttonContainer}>
              <TouchableOpacity style={[styles.updateButton, {width: '100%'}]} onPress={dismissModal}>
                <Text style={styles.updateButtonText}>OK</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    );
  }

  if (errorMessage) {
    return (
      <Modal
        transparent={true}
        animationType="fade"
        visible={true}
        onRequestClose={dismissModal}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.updateModal}>
            <MaterialIcons name="error" size={40} color="#F44336" style={styles.modalIcon} />
            <Text style={styles.modalTitle}>Update Error</Text>
            <Text style={styles.modalText}>
              {errorMessage}
            </Text>
            <TouchableOpacity style={styles.updateButton} onPress={dismissModal}>
              <Text style={styles.updateButtonText}>OK</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    );
  }

  // Update the update available modal to remove the "Later" button
  return (
    <Modal
      transparent={true}
      animationType="fade"
      visible={updateAvailable}
      onRequestClose={() => {}} // Empty function to prevent back button from dismissing
    >
      <View style={styles.modalOverlay}>
        <View style={styles.updateModal}>
          <FontAwesome name="download" size={40} color="#6777E8" style={styles.modalIcon} />
          <Text style={styles.modalTitle}>Update Available</Text>
          <Text style={styles.modalText}>
            A new version of the app is available with the latest features and improvements.
            You must update to continue using the app.
          </Text>
          
          {/* {releaseNotes && (
            <View style={styles.releaseNotesContainer}>
              <Text style={styles.releaseNotesTitle}>What's new in this update:</Text>
              <ScrollView style={styles.releaseNotesScroll}>
                <Text style={styles.releaseNotesText}>{releaseNotes}</Text>
              </ScrollView>
            </View>
          )} */}
          
          <View style={styles.buttonContainer}>
            <TouchableOpacity style={[styles.updateButton, {width: '100%'}]} onPress={fetchAndApplyUpdate}>
              <Text style={styles.updateButtonText}>Update Now</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

// Keep your existing styles and add these new ones
const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  updateModal: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 24,
    width: '85%',
    alignItems: 'center',
  },
  modalIcon: {
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#333',
  },
  modalText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 22,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  laterButton: {
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ccc',
    flex: 1,
    marginRight: 8,
    alignItems: 'center',
  },
  laterButtonText: {
    color: '#666',
    fontWeight: '600',
  },
  updateButton: {
    backgroundColor: '#6777E8',
    padding: 12,
    borderRadius: 8,
    flex: 1,
    marginLeft: 8,
    alignItems: 'center',
  },
  updateButtonText: {
    color: 'white',
    fontWeight: '600',
  },
  updateCard: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 24,
    width: '85%',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  updateIcon: {
    marginBottom: 16,
  },
  updateTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#333',
  },
  updateSubtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 24,
  },
  progressBarContainer: {
    height: 10,
    width: '100%',
    backgroundColor: '#e0e0e0',
    borderRadius: 5,
    marginBottom: 8,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    backgroundColor: '#6777E8',
  },
  progressText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 16,
  },
  updateNote: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
    fontStyle: 'italic',
  },
  releaseNotesContainer: {
    width: '100%',
    marginBottom: 20,
    backgroundColor: '#f9f9f9',
    borderRadius: 10,
    padding: 15,
  },
  releaseNotesTitle: {
    fontWeight: 'bold',
    fontSize: 16,
    marginBottom: 8,
    color: '#333',
  },
  releaseNotesScroll: {
    maxHeight: 150,
  },
  releaseNotesText: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
});

export default UpdateModal;