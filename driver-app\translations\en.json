{"uid": {"title": "UID", "placeholder": "Enter Your Unique Identity Pin", "helpText": "Need Help?", "nextButton": "Next →", "errors": {"invalid": "<PERSON><PERSON>id uid", "general": "An error occurred. Please try again."}}, "number": {"phoneLabel": "Phone Number", "phonePlaceholder": "Enter your phone number", "busLabel": "Bus Number", "busPlaceholder": "Search for your bus number", "verifyButton": "Verify", "startButton": "Start →", "errors": {"invalidPhone": "Invalid phone number", "driverNotFound": "Driver data not found"}}, "tracking": {"startRide": "START RIDE", "stopRide": "STOP RIDE", "stoppingRide": "Stopping...", "startingRide": "Starting the ride please wait...", "resumeButton": "RESUME", "from": "From", "to": "To", "busInfo": "Bus {{number}}", "lastStop": "Last Stop", "confirmStop": "Are you sure you want to stop the ride?", "yes": "Yes", "no": "No", "resumeRide": "Resume Ride", "resumeQuestion": "Do you want to resume the previous ride?", "newRide": "Start New Ride", "alerts": {"permissionDeniedTitle": "Permission Denied", "enableLocationPermission": "Please enable location permission.", "enableBackgroundPermission": "Please enable background location permission.", "enableNotificationPermission": "Please enable notification permission to keep tracking active.", "errorTitle": "Error", "unableToStartTracking": "Unable to start location tracking.", "missingInfo": "Missing bus number or phone number. Please restart the app.", "failedReleaseNotes": "Failed to show release notes.", "errorStoppingRide": "An error occurred while stopping the ride. Please try again."}, "notification": {"title": "Location Tracking Active", "body": "Bus {{busNumber}} is currently being tracked."}, "buttonState": {"running": "Running", "start": "Start"}, "comingSoon": {"title": "Coming Soon", "fuelData": "Fuel data feature will be available soon.", "settings": "Settings feature will be available soon.", "profile": "Profile feature will be available soon."}, "route": {"mayiladuthurai": "Mayiladuthurai", "karaikal": "Karaikal"}, "loading": {"busData": "Loading bus data..."}, "resumeModal": {"title": "Resume Previous Ride?", "message": "We detected a previous ride. Would you like to resume it or start a new one?", "resumeButton": "Resume Ride", "newRideButton": "New Ride"}, "stopModal": {"title": "Stop Tracking?", "message": "Are you sure you want to stop tracking this bus?", "cancelButton": "Cancel", "confirmButton": "Stop", "confirmStopTitle": "Confirm Stop Tracking", "waitMessage": "Please wait {{seconds}} seconds...", "readyMessage": "You can now confirm to stop."}, "lastStopModal": {"title": "Last Stop Reached", "message": "Bus {{busN<PERSON>ber}} has reached the last stop. What would you like to do?", "endJourneyButton": "End Journey", "startNewJourneyButton": "Start New Journey", "confirmEndTitle": "Confirm End Journey", "confirmStartTitle": "Confirm New Journey", "enterBusNumber": "Please enter the bus number to confirm this action", "busNumberPlaceholder": "Enter bus number", "busNumberMismatch": "Bus number doesn't match. Please try again.", "currentBusNumber": "Current Bus Number"}, "releaseNotesModal": {"closeButton": "Close"}, "statusActive": "Active", "statusInactive": "Inactive", "printTicket": "Print Ticket", "printTicketDisabled": "First start the ride to print ticket"}, "common": {"loading": "Loading...", "cancel": "Cancel", "confirm": "Confirm", "logout": "Logout", "ok": "OK", "settings": "Settings"}, "network": {"noConnectionTitle": "No Internet Connection", "noConnectionMessage": "Please check your internet connection and try again. The app will automatically resume when connection is restored.", "exitButton": "Exit App", "connectionRestoredTitle": "Connection Restored", "connectionRestoredMessage": "Refreshing data to ensure everything is up to date."}, "ticket": {"title": "Ticket", "fareAmount": "<PERSON><PERSON> Amount", "fromLabel": "From", "toLabel": "To", "selectDeparture": "Select departure city", "selectDestination": "Select destination city", "passengerTypes": {"child": "Child", "adult": "Adult", "busPass": "Bus Pass"}, "buttons": {"print": "Print", "luggage": "Luggage", "cancel": "Cancel"}, "alerts": {"printTitle": "Print Ticket", "printMessage": "Ticket will be printed", "luggageTitle": "Luggage", "luggageMessage": "Luggage option selected", "cancelTitle": "Cancel Ticket", "cancelMessage": "Are you sure you want to cancel this ticket?"}}}