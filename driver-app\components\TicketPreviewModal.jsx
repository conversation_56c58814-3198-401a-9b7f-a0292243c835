import React, { useContext } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  Alert
} from 'react-native';
import { ThemeContext } from '../contexts/ThemeContext';
import FontAwesome6 from '@expo/vector-icons/FontAwesome6';
import { LinearGradient } from 'expo-linear-gradient';

export default function TicketPreviewModal({
  visible,
  onClose,
  ticketData,
  onBluetoothPrint,
  onPDFPrint,
  onShowSetupGuide,
  isPrinting,
  connectedPrinter,
  modalHeight = '80%' // Allow customizable height, default 80%
}) {
  const { theme, mode } = useContext(ThemeContext);

  const handleBluetoothPrint = () => {
    onClose();
    onBluetoothPrint();
  };

  const handlePDFPrint = () => {
    onClose();
    onPDFPrint();
  };

  const handleSetupGuide = () => {
    onClose();
    onShowSetupGuide();
  };

  const styles = StyleSheet.create({
    modalOverlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'center',
      alignItems: 'center',
    },
    modalContent: {
      width: '90%',
      height: modalHeight, // Use customizable height
      backgroundColor: theme.background,
      borderRadius: 15,
      padding: 0,
      overflow: 'hidden',
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: 20,
      paddingVertical: 15,
      backgroundColor: theme.secondaryBackground,
      borderBottomWidth: 1,
      borderBottomColor: theme.borderColor,
    },
    title: {
      fontSize: 18,
      fontWeight: 'bold',
      color: theme.textColor,
    },
    closeButton: {
      padding: 5,
      borderRadius: 15,
      backgroundColor: theme.background,
    },
    content: {
      flex: 1,
      paddingHorizontal: 20,
      paddingTop: 15,
      paddingBottom: 10,
    },
    ticketContainer: {
      backgroundColor: mode === 'dark' ? '#2A2A2A' : '#FFFFFF',
      borderRadius: 12,
      padding: 18,
      marginBottom: 10,
      borderWidth: 2,
      borderStyle: 'dashed',
      borderColor: theme.borderColor,
    },
    ticketHeader: {
      alignItems: 'center',
      marginBottom: 15,
      paddingBottom: 15,
      borderBottomWidth: 1,
      borderBottomStyle: 'dashed',
      borderBottomColor: theme.borderColor,
    },
    companyName: {
      fontSize: 18,
      fontWeight: 'bold',
      color: theme.textColor,
      marginBottom: 5,
      letterSpacing: 1,
    },
    ticketTitle: {
      fontSize: 14,
      color: theme.secondary || theme.textColor,
    },
    separator: {
      textAlign: 'center',
      color: theme.secondary || theme.textColor,
      marginVertical: 10,
      fontSize: 12,
    },
    infoRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: 8,
      paddingVertical: 2,
    },
    label: {
      fontSize: 14,
      fontWeight: '600',
      color: theme.secondary || theme.textColor,
    },
    value: {
      fontSize: 14,
      fontWeight: 'bold',
      color: theme.textColor,
    },
    routeContainer: {
      backgroundColor: theme.primary + '20',
      borderColor: theme.primary,
      borderWidth: 1,
      borderRadius: 8,
      padding: 12,
      marginVertical: 15,
      alignItems: 'center',
    },
    routeText: {
      fontSize: 16,
      fontWeight: 'bold',
      color: theme.primary,
      textAlign: 'center',
    },
    fareSection: {
      borderTopWidth: 1,
      borderBottomWidth: 1,
      borderTopStyle: 'dashed',
      borderBottomStyle: 'dashed',
      borderTopColor: theme.borderColor,
      borderBottomColor: theme.borderColor,
      paddingVertical: 12,
      marginVertical: 15,
    },
    totalFare: {
      fontSize: 16,
      fontWeight: 'bold',
      color: '#FF0000',
      borderTopWidth: 1,
      borderTopColor: theme.borderColor,
      paddingTop: 8,
      marginTop: 8,
    },
    footer: {
      alignItems: 'center',
      marginTop: 15,
      paddingTop: 15,
      borderTopWidth: 1,
      borderTopStyle: 'dashed',
      borderTopColor: theme.borderColor,
    },
    footerText: {
      fontSize: 14,
      color: theme.textColor,
      marginBottom: 5,
    },
    footerSubText: {
      fontSize: 12,
      color: theme.secondary || theme.textColor,
    },
    buttonsContainer: {
      paddingHorizontal: 20,
      paddingVertical: 15,
      backgroundColor: theme.secondaryBackground,
      borderTopWidth: 1,
      borderTopColor: theme.borderColor,
    },
    printerStatus: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      marginBottom: 15,
      padding: 10,
      borderRadius: 8,
      backgroundColor: connectedPrinter ? theme.success + '20' : theme.warning + '20',
      borderColor: connectedPrinter ? theme.success : theme.warning,
      borderWidth: 1,
    },
    printerStatusText: {
      marginLeft: 8,
      fontSize: 14,
      fontWeight: '600',
      color: connectedPrinter ? theme.success : theme.warning,
    },
    buttonRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: 10,
    },
    button: {
      flex: 1,
      marginHorizontal: 5,
      borderRadius: 10,
      overflow: 'hidden',
    },
    buttonInner: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 12,
      paddingHorizontal: 15,
    },
    buttonText: {
      color: 'white',
      fontWeight: 'bold',
      marginLeft: 8,
      fontSize: 14,
    },
    secondaryButton: {
      backgroundColor: theme.secondaryBackground,
      borderColor: theme.borderColor,
      borderWidth: 1,
      borderRadius: 10,
      marginHorizontal: 5,
      flex: 1,
    },
    secondaryButtonText: {
      color: theme.textColor,
      fontWeight: '600',
      marginLeft: 8,
      fontSize: 14,
    },
  });

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          {/* Header */}
          <View style={styles.header}>
            <Text style={styles.title}>Ticket Preview</Text>
            <TouchableOpacity style={styles.closeButton} onPress={onClose}>
              <FontAwesome6 name="xmark" size={20} color={theme.textColor} />
            </TouchableOpacity>
          </View>

          {/* Ticket Preview */}
          <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
            <View style={styles.ticketContainer}>
              {/* Header */}
              <View style={styles.ticketHeader}>
                <Text style={styles.companyName}>BUS TRANSPORT</Text>
                <Text style={styles.ticketTitle}>TICKET RECEIPT</Text>
              </View>

              <Text style={styles.separator}>--------------------------------</Text>

              {/* Ticket Info */}
              <View style={styles.infoRow}>
                <Text style={styles.label}>Ticket No:</Text>
                <Text style={styles.value}>{ticketData?.ticketNumber}</Text>
              </View>
              <View style={styles.infoRow}>
                <Text style={styles.label}>Date:</Text>
                <Text style={styles.value}>{ticketData?.date}</Text>
              </View>
              <View style={styles.infoRow}>
                <Text style={styles.label}>Time:</Text>
                <Text style={styles.value}>{ticketData?.time}</Text>
              </View>
              <View style={styles.infoRow}>
                <Text style={styles.label}>Bus No:</Text>
                <Text style={styles.value}>{ticketData?.busNumber}</Text>
              </View>

              {/* Route */}
              <View style={styles.routeContainer}>
                <Text style={styles.routeText}>
                  {ticketData?.fromCity} TO {ticketData?.toCity}
                </Text>
              </View>

              {/* Fare Section */}
              <View style={styles.fareSection}>
                <View style={styles.infoRow}>
                  <Text style={styles.label}>Passenger Type:</Text>
                  <Text style={styles.value}>{ticketData?.passengerType}</Text>
                </View>
                <View style={styles.infoRow}>
                  <Text style={styles.label}>Quantity:</Text>
                  <Text style={styles.value}>1</Text>
                </View>
                <View style={[styles.infoRow, styles.totalFare]}>
                  <Text style={styles.label}>Total Fare:</Text>
                  <Text style={styles.value}>Rs {ticketData?.fareAmount}</Text>
                </View>
              </View>

              <Text style={styles.separator}>--------------------------------</Text>

              {/* Footer */}
              <View style={styles.footer}>
                <Text style={styles.footerText}>Have a nice journey!</Text>
                <Text style={styles.footerSubText}>Thank you for choosing our service</Text>
              </View>
            </View>
          </ScrollView>

          {/* Action Buttons */}
          <View style={styles.buttonsContainer}>
            {/* Printer Status */}
            <View style={styles.printerStatus}>
              <FontAwesome6
                name="print"
                size={16}
                color={connectedPrinter ? theme.success : theme.warning}
              />
              <Text style={styles.printerStatusText}>
                {connectedPrinter ? `Connected: ${connectedPrinter.name}` : 'No Bluetooth Printer Connected'}
              </Text>
            </View>

            {/* Print Buttons */}
            <View style={styles.buttonRow}>
              <LinearGradient
                colors={connectedPrinter ? ['#6777E8', '#95B2EC'] : ['#95A5A6', '#BDC3C7']}
                style={styles.button}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
              >
                <TouchableOpacity
                  style={styles.buttonInner}
                  onPress={handleBluetoothPrint}
                  disabled={!connectedPrinter || isPrinting}
                >
                  <FontAwesome6 name="bluetooth" size={16} color="white" />
                  <Text style={styles.buttonText}>
                    {isPrinting ? 'Printing...' : 'Bluetooth Print'}
                  </Text>
                </TouchableOpacity>
              </LinearGradient>

              <LinearGradient
                colors={['#E74C3C', '#C0392B']}
                style={styles.button}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
              >
                <TouchableOpacity
                  style={styles.buttonInner}
                  onPress={handlePDFPrint}
                  disabled={isPrinting}
                >
                  <FontAwesome6 name="file-pdf" size={16} color="white" />
                  <Text style={styles.buttonText}>PDF/Share</Text>
                </TouchableOpacity>
              </LinearGradient>
            </View>

            {/* Secondary Button */}
            <TouchableOpacity style={styles.secondaryButton} onPress={handleSetupGuide}>
              <View style={styles.buttonInner}>
                <FontAwesome6 name="gear" size={16} color={theme.textColor} />
                <Text style={styles.secondaryButtonText}>Setup Guide</Text>
              </View>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
}
